{% extends 'base.html' %}

{% block title %}食堂管理仪表盘 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='css/process_navigation.css') }}">
<style nonce="{{ csp_nonce }}">
    .dashboard-card {
        transition: all 0.3s;
        height: 100%;
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .menu-card {
        height: 100%;
    }
    .menu-item {
        border-bottom: 1px solid #eee;
        padding: 8px 0;
    }
    .menu-item:last-child {
        border-bottom: none;
    }
    .status-badge {
        font-size: 0.8rem;
    }
    .section-title {
        border-left: 4px solid var(--primary);
        padding-left: 10px;
        margin-bottom: 20px;
    }
    .quick-actions .btn {
        margin-bottom: 10px;
    }
    .function-card {
        transition: all 0.3s;
        cursor: pointer;
        height: 100%;
    }
    .function-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .function-icon {
        font-size: 2rem;
        margin-bottom: 10px;
    }
    .companion-card {
        border-bottom: 1px solid #eee;
        padding: 10px 0;
    }
    .companion-card:last-child {
        border-bottom: none;
    }
    .qr-code-container {
        text-align: center;
        margin: 15px 0;
    }
    .qr-code-img {
        max-width: 150px;
        margin: 0 auto;
    }
    /* 加载状态样式 */
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        border-radius: 0.25rem;
    }
    .loading-spinner {
        width: 3rem;
        height: 3rem;
    }
    .card {
        position: relative;
    }
    .error-message {
        color: #dc3545;
        text-align: center;
        padding: 10px;
    }
    .retry-button {
        display: block;
        margin: 10px auto;
    }
</style>
{% endblock %}

{% block content %}
<!-- 导入流程导航组件 -->
{% from 'components/process_navigation.html' import process_navigation %}

<!-- 新用户欢迎信息 -->
{% if is_new_user %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-success alert-dismissible fade show" style="background: linear-gradient(135deg, #28a745, #20c997); border: none; color: white;">
                <button type="button" class="close text-white" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="d-flex align-items-center">
                    <div class="mr-3">
                        <i class="fas fa-graduation-cap fa-3x"></i>
                    </div>
                    <div>
                        <h4 class="alert-heading mb-2">🎉 欢迎加入校园餐智慧食堂平台！</h4>
                        <p class="mb-2">恭喜您成功创建 <strong>{{ current_user.area.name }}</strong> 的管理账号！</p>
                        <p class="mb-2">您现在拥有完整的食堂管理权限，可以开始使用以下功能：</p>
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check-circle mr-2"></i>日常工作日志管理</li>
                                    <li><i class="fas fa-check-circle mr-2"></i>食品安全检查记录</li>
                                    <li><i class="fas fa-check-circle mr-2"></i>陪餐记录管理</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check-circle mr-2"></i>菜单计划制定</li>
                                    <li><i class="fas fa-check-circle mr-2"></i>食品溯源管理</li>
                                    <li><i class="fas fa-check-circle mr-2"></i>留样记录管理</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check-circle mr-2"></i>仓库库存管理</li>
                                    <li><i class="fas fa-check-circle mr-2"></i>采购订单管理</li>
                                    <li><i class="fas fa-check-circle mr-2"></i>供应商管理</li>
                                </ul>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-light btn-sm mr-2" data-data-data-data-data-onclick="startUserGuide()" data-event-id="037b2e69" data-event-id="037b2e69" data-event-id="037b2e69" data-event-id="037b2e69" data-event-id="037b2e69">
                                <i class="fas fa-graduation-cap mr-1"></i>开始系统引导
                            </button>
                            <a href="{{ url_for('daily_management.logs') }}" class="btn btn-outline-light btn-sm mr-2">
                                <i class="fas fa-play mr-1"></i>创建工作日志
                            </a>
                            <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-outline-light btn-sm mr-2">
                                <i class="fas fa-calendar-alt mr-1"></i>制定菜单计划
                            </a>
                            <a href="{{ url_for('warehouse.index') }}" class="btn btn-outline-light btn-sm">
                                <i class="fas fa-warehouse mr-1"></i>查看仓库管理
                            </a>
                        </div>
                        <div class="mt-2">
                            <small class="text-light">
                                <i class="fas fa-info-circle mr-1"></i>
                                系统已为您自动创建了默认仓库和三个储存位置（储存室、冷藏区、冷冻区），您可以立即开始使用库存管理功能！
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endif %}

<div class="row mb-4">
    <div class="col-md-8">
        <h2>{{ current_user.area.name if current_user.area else '食堂管理' }}仪表盘</h2>
        <p class="text-muted">欢迎回来，{{ current_user.real_name or current_user.username }}</p>
    </div>
    <div class="col-md-4 text-right">
        <button id="refreshDashboard" class="btn btn-info">
            <i class="fas fa-sync-alt"></i> 刷新数据
        </button>
        <a href="{{ url_for('daily_management.index') }}" class="btn btn-primary">
            <i class="fas fa-clipboard-list"></i> 日常管理
        </a>
    </div>
</div>

<!-- 食堂管理流程导航 -->
<div class="row mb-4">
    <div class="col-12">
        {{ process_navigation(
            menu_plan=menu_plan,
            purchase_plan=purchase_plan,
            inspection=inspection,
            storage_in=storage_in,
            consumption_plan=consumption_plan,
            storage_out=storage_out,
            inventory=inventory,
            samples=samples,
            tracing=tracing,
            progress_percentage=progress_percentage,
            today_tasks=today_tasks,
            available_routes=[]
        ) }}
    </div>
</div>

<!-- 日常管理六大功能 -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="section-title">日常管理功能</h4>
    </div>
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row" id="daily-management-functions">
                    <div class="col-md-2 col-sm-4 mb-4">
                        <a href="{{ url_for('daily_management.logs') }}" class="text-decoration-none">
                            <div class="card function-card text-center">
                                <div class="card-body">
                                    <div class="function-icon"><i class="fas fa-clipboard-list"></i></div>
                                    <h5>工作日志</h5>
                                    <p class="text-muted small mt-2">记录日常工作</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2 col-sm-4 mb-4">
                        <a href="{{ url_for('daily_management.scan_upload_entry') }}" class="text-decoration-none">
                            <div class="card function-card text-center">
                                <div class="card-body">
                                    <div class="function-icon"><i class="fas fa-clipboard-check"></i></div>
                                    <h5>检查记录</h5>
                                    <p class="text-muted small mt-2">食品安全检查</p>
                                    <span class="badge badge-info mt-2">
                                        <i class="fas fa-qrcode"></i> 支持扫码上传
                                    </span>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2 col-sm-4 mb-4">
                        <a href="#" class="text-decoration-none companion-entry-link">
                            <div class="card function-card text-center">
                                <div class="card-body">
                                    <div class="function-icon"><i class="fas fa-utensils"></i></div>
                                    <h5>陪餐记录</h5>
                                    <p class="text-muted small mt-2">领导陪餐记录</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2 col-sm-4 mb-4">
                        <a href="#" class="text-decoration-none training-entry-link">
                            <div class="card function-card text-center">
                                <div class="card-body">
                                    <div class="function-icon"><i class="fas fa-chalkboard-teacher"></i></div>
                                    <h5>培训记录</h5>
                                    <p class="text-muted small mt-2">员工培训记录</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2 col-sm-4 mb-4">
                        <a href="#" class="text-decoration-none event-entry-link">
                            <div class="card function-card text-center">
                                <div class="card-body">
                                    <div class="function-icon"><i class="fas fa-calendar-day"></i></div>
                                    <h5>特殊事件</h5>
                                    <p class="text-muted small mt-2">特殊事件记录</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2 col-sm-4 mb-4">
                        <a href="#" class="text-decoration-none issue-entry-link">
                            <div class="card function-card text-center">
                                <div class="card-body">
                                    <div class="function-icon"><i class="fas fa-exclamation-triangle"></i></div>
                                    <h5>问题记录</h5>
                                    <p class="text-muted small mt-2">问题跟踪记录</p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 今日菜单 -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="section-title">今日菜单</h4>
    </div>
    <div class="col-md-4">
        <div class="card menu-card mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">早餐</h5>
            </div>
            <div class="card-body" id="breakfast-menu">
                {% if today_menu and today_menu['早餐'] %}
                    <div class="mb-2">
                        <span class="badge badge-{% if today_menu['早餐'].status == '已执行' %}success{% elif today_menu['早餐'].status == '已发布' %}primary{% else %}secondary{% endif %} status-badge">{{ today_menu['早餐'].status }}</span>
                        {% if today_menu['早餐'].expected_diners %}
                            <span class="ml-2">预计就餐人数: {{ today_menu['早餐'].expected_diners }}</span>
                        {% endif %}
                    </div>
                    {% if today_menu['早餐'].recipes %}
                        <div class="mt-3">
                            {% for recipe in today_menu['早餐'].recipes %}
                                <div class="menu-item">
                                    <div class="d-flex justify-content-between">
                                        <div>{{ recipe.name }}</div>
                                        {% if recipe.quantity %}
                                            <div>{{ recipe.quantity }} 份</div>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-2"><p class="text-muted">暂无菜品</p></div>
                    {% endif %}
                {% else %}
                    <div class="text-center py-4">
                        <p class="text-muted">暂无早餐菜单</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card menu-card mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">午餐</h5>
            </div>
            <div class="card-body" id="lunch-menu">
                <div class="text-center py-4">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="sr-only">加载中...</span>
                    </div>
                    <span class="ml-2 text-muted">加载中...</span>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card menu-card mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">晚餐</h5>
            </div>
            <div class="card-body" id="dinner-menu">
                <div class="text-center py-4">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="sr-only">加载中...</span>
                    </div>
                    <span class="ml-2 text-muted">加载中...</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 陪餐记录和二维码 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">最近陪餐记录</h5>
                <a href="{{ url_for('daily_management.index') }}" class="btn btn-sm btn-primary">查看全部</a>
            </div>
            <div class="card-body p-0" id="recent-companions">
                <div class="table-responsive">
                    <table class="table table-sm table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th class="w-15">陪餐人</th>
                                <th style="width: 10%;">角色</th>
                                <th style="width: 10%;">餐次</th>
                                <th style="width: 20%;">陪餐时间</th>
                                <th class="w-15">口味评分</th>
                                <th class="w-15">卫生评分</th>
                                <th class="w-15">服务评分</th>
                            </tr>
                        </thead>
                        <tbody id="companions-table-body">
                            <tr>
                                <td colspan="7" class="text-center py-3">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="sr-only">加载中...</span>
                                    </div>
                                    <span class="ml-2 text-muted">加载中...</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer text-center">
                <a href="#" class="btn btn-info" id="add-companion-btn">
                    <i class="fas fa-plus"></i> 添加陪餐记录
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">陪餐记录二维码</h5>
            </div>
            <div class="card-body text-center">
                <p>扫描下方二维码快速添加陪餐记录</p>
                <div class="qr-code-container">
                    <img src="{{ url_for('static', filename='img/qr-code-placeholder.png') }}" alt="陪餐记录二维码" class="qr-code-img" id="companion-qr-code">
                </div>
                <button class="btn btn-outline-primary mt-3" id="generate-qr-code-btn">
                    <i class="fas fa-qrcode"></i> 生成二维码
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 溯源和留样 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">食品溯源</h5>
            </div>
            <div class="card-body">
                <p>通过食品溯源系统，可以追踪食材从采购到加工的全过程。</p>
                <div class="text-center py-3">
                    <i class="fas fa-search-location fa-4x text-primary mb-3"></i>
                    <p>扫描食品包装上的二维码，即可查看食品详细信息</p>
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('food_trace.index') }}" class="btn btn-primary">
                        <i class="fas fa-qrcode"></i> 扫码溯源
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">食品留样</h5>
                <span class="badge badge-primary" id="food-samples-count">-</span>
            </div>
            <div class="card-body">
                <p>今日食品留样情况统计</p>
                <div class="text-center py-3">
                    <i class="fas fa-vial fa-4x text-success mb-3"></i>
                    <p>一键完成食品留样记录，确保食品安全</p>
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('food_trace.one_click_sample') }}" class="btn btn-success">
                        <i class="fas fa-plus"></i> 一键留样
                    </a>
                    <a href="{{ url_for('food_sample.index') }}" class="btn btn-outline-secondary ml-2">
                        <i class="fas fa-list"></i> 查看记录
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="section-title">快速操作</h4>
    </div>
    <div class="col-md-12">
        <div class="card">
            <div class="card-body quick-actions">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-primary btn-block" id="create-daily-log-btn">
                            <i class="fas fa-clipboard"></i> 创建今日工作日志
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-success btn-block" id="add-inspection-btn">
                            <i class="fas fa-clipboard-check"></i> 添加检查记录
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('daily_management.scan_upload_entry') }}" class="btn btn-info btn-block" style="background: linear-gradient(135deg, #36b9cc, #1cc88a); position: relative; overflow: hidden;">
                            <i class="fas fa-qrcode"></i> 扫码上传
                            <span class="badge badge-light" style="position: absolute; top: -8px; right: -8px; border-radius: 50%; padding: 0.25rem; font-size: 0.6rem; box-shadow: 0 2px 5px rgba(0,0,0,0.2);">
                                <i class="fas fa-star"></i>
                            </span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-info btn-block" id="add-companion-btn2">
                            <i class="fas fa-utensils"></i> 添加陪餐记录
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-warning btn-block" id="add-issue-btn">
                            <i class="fas fa-exclamation-triangle"></i> 添加问题记录
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-secondary btn-block" id="add-training-btn">
                            <i class="fas fa-chalkboard-teacher"></i> 添加培训记录
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-danger btn-block" id="add-event-btn">
                            <i class="fas fa-calendar-day"></i> 添加特殊事件
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('food_trace.one_click_sample') }}" class="btn btn-dark btn-block">
                            <i class="fas fa-vial"></i> 添加留样记录
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-light btn-block">
                            <i class="fas fa-calendar-alt"></i> 菜单计划管理
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

<!-- 引导模态框 -->
{% include 'guide/step_modal.html' %}

<!-- 场景选择模态框 -->
{% include 'guide/scenario_selection.html' %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/process_navigation.js') }}"></script>
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/user_guide.js') }}"></script>
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 绑定二维码生成按钮
        $('#generate-qr-code-btn').click(function() {
            generateCompanionQRCode();
        });

        // 绑定快速操作按钮
        bindQuickActionButtons();

        // 加载陪餐记录数据
        loadRecentCompanions();

        // 加载今日菜单数据
        loadTodayMenu();

        // 绑定功能图标点击事件
        bindFunctionCardClicks();

        // 绑定刷新按钮
        $('#refreshDashboard').click(function() {
            loadRecentCompanions();
            loadTodayMenu();
        });
    });

    // 启动用户引导
    function startUserGuide() {
        if (typeof userGuide !== 'undefined') {
            // 显示场景选择模态框
            $('#scenarioSelectionModal').modal('show');
        } else {
            console.error('用户引导系统未初始化');
        }
    }

    // 直接启动引导（跳过场景选择）
    function startGuideDirectly() {
        if (typeof userGuide !== 'undefined') {
            userGuide.showStep('welcome');
        } else {
            console.error('用户引导系统未初始化');
        }
    }

    // 生成陪餐记录二维码
    function generateCompanionQRCode() {
        try {
            // 显示加载状态
            var qrCodeImg = $('#companion-qr-code');
            var originalSrc = qrCodeImg.attr('src');
            qrCodeImg.attr('src', "{{ url_for('static', filename='img/loading.gif') }}");

            // 获取当前日期
            var today = new Date().toISOString().split('T')[0];

            // 使用外部服务生成二维码
            var qrData = encodeURIComponent(window.location.origin + "{{ url_for('daily_management.index') }}" + '?date=' + today);
            var qrServiceUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=' + qrData;

            // 模拟API调用
            setTimeout(function() {
                qrCodeImg.attr('src', qrServiceUrl);
                alert('二维码生成成功');
            }, 500);
        } catch (e) {
            console.error('生成二维码失败:', e);
            $('#companion-qr-code').attr('src', originalSrc || "{{ url_for('static', filename='img/qr-code-placeholder.png') }}");
            alert('二维码生成失败');
        }
    }

    // 绑定快速操作按钮
    function bindQuickActionButtons() {
        // 创建今日工作日志
        $('#create-daily-log-btn').click(function(e) {
            e.preventDefault();
            window.location.href = "{{ url_for('daily_management.logs') }}";
        });

        // 添加检查记录
        $('#add-inspection-btn').click(function(e) {
            e.preventDefault();
            window.location.href = "{{ url_for('daily_management.index') }}";
        });

        // 检查记录二维码 - 已经改为直接链接到扫码上传入口
        // $('#inspection-qrcode-btn').click(function(e) {
        //     e.preventDefault();
        //     // 获取今天的日期
        //     var today = new Date().toISOString().split('T')[0];
        //     // 先跳转到日志页面，然后再跳转到检查记录页面
        //     window.location.href = "{{ url_for('daily_management.inspections_by_date', date_str='') }}" + today;
        // });

        // 添加陪餐记录
        $('#add-companion-btn, #add-companion-btn2').click(function(e) {
            e.preventDefault();
            window.location.href = "{{ url_for('daily_management.index') }}";
        });

        // 添加培训记录
        $('#add-training-btn').click(function(e) {
            e.preventDefault();
            window.location.href = "{{ url_for('daily_management.index') }}";
        });

        // 添加特殊事件
        $('#add-event-btn').click(function(e) {
            e.preventDefault();
            window.location.href = "{{ url_for('daily_management.index') }}";
        });

        // 添加问题记录
        $('#add-issue-btn').click(function(e) {
            e.preventDefault();
            window.location.href = "{{ url_for('daily_management.index') }}";
        });
    }

    // 加载最近陪餐记录
    function loadRecentCompanions() {
        $.ajax({
            url: '/api/v2/dining-companions/recent',
            type: 'GET',
            data: { limit: 5 },
            success: function(data) {
                var tbody = $('#companions-table-body');
                tbody.empty();

                if (data && data.length > 0) {
                    data.forEach(function(companion) {
                        var row = '<tr>';
                        row += '<td>' + (companion.name || '-') + '</td>';
                        row += '<td><span class="badge badge-secondary">' + (companion.role || '-') + '</span></td>';
                        row += '<td><span class="badge badge-' + getMealTypeBadgeClass(companion.meal_type) + '">' + getMealTypeText(companion.meal_type) + '</span></td>';
                        row += '<td>' + (companion.date || '-') + ' ' + (companion.time || '-') + '</td>';
                        row += '<td>' + generateStarRating(companion.taste_rating) + '</td>';
                        row += '<td>' + generateStarRating(companion.hygiene_rating) + '</td>';
                        row += '<td>' + generateStarRating(companion.service_rating) + '</td>';
                        row += '</tr>';
                        tbody.append(row);
                    });
                } else {
                    tbody.append('<tr><td colspan="7" class="text-center py-3 text-muted">暂无陪餐记录</td></tr>');
                }
            },
            error: function(xhr, status, error) {
                var tbody = $('#companions-table-body');
                tbody.empty();
                var errorMessage = '加载失败，请稍后重试';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                tbody.append('<tr><td colspan="7" class="text-center py-3 text-danger">' + errorMessage + '</td></tr>');
                console.error('加载陪餐记录失败:', error);
            }
        });
    }

    // 生成星级评分HTML
    function generateStarRating(rating) {
        if (!rating || rating === 0) {
            return '<span class="text-muted">-</span>';
        }

        var stars = '';
        for (var i = 1; i <= 5; i++) {
            if (i <= rating) {
                stars += '<i class="fas fa-star text-warning"></i>';
            } else {
                stars += '<i class="far fa-star text-muted"></i>';
            }
        }
        return stars;
    }

    // 获取餐次徽章样式
    function getMealTypeBadgeClass(mealType) {
        switch(mealType) {
            case 'breakfast': return 'info';
            case 'lunch': return 'success';
            case 'dinner': return 'warning';
            default: return 'secondary';
        }
    }

    // 获取餐次文本
    function getMealTypeText(mealType) {
        switch(mealType) {
            case 'breakfast': return '早餐';
            case 'lunch': return '午餐';
            case 'dinner': return '晚餐';
            default: return mealType || '-';
        }
    }

    // 加载今日菜单
    function loadTodayMenu() {
        $.ajax({
            url: '/api/v2/dashboard/today-menu',
            type: 'GET',
            success: function(response) {
                if (response.success && response.data) {
                    updateMenuDisplay(response.data);
                } else {
                    showMenuError('获取菜单失败');
                }
            },
            error: function() {
                showMenuError('网络错误，无法获取菜单');
            }
        });
    }

    // 更新菜单显示
    function updateMenuDisplay(menuData) {
        // 更新早餐
        updateMealDisplay('breakfast', menuData['早餐']);
        // 更新午餐
        updateMealDisplay('lunch', menuData['午餐']);
        // 更新晚餐
        updateMealDisplay('dinner', menuData['晚餐']);
    }

    // 更新单个餐次显示
    function updateMealDisplay(mealId, mealData) {
        var containerId = mealId === 'breakfast' ? '#breakfast-menu' :
                         mealId === 'lunch' ? '#lunch-menu' : '#dinner-menu';
        var container = $(containerId);

        if (!mealData || !mealData.recipes || mealData.recipes.length === 0) {
            container.html('<div class="text-center py-4"><p class="text-muted">暂无菜单</p></div>');
            return;
        }

        var html = '<div class="mb-2">';
        html += '<span class="badge badge-' + getStatusBadgeClass(mealData.status) + ' status-badge">' + mealData.status + '</span>';
        if (mealData.expected_diners) {
            html += '<span class="ml-2">预计就餐人数: ' + mealData.expected_diners + '</span>';
        }
        html += '</div>';

        if (mealData.recipes.length > 0) {
            html += '<div class="mt-3">';
            mealData.recipes.forEach(function(recipe) {
                html += '<div class="menu-item">';
                html += '<div class="d-flex justify-content-between">';
                html += '<div>' + recipe.name + '</div>';
                if (recipe.quantity) {
                    html += '<div>' + recipe.quantity + ' 份</div>';
                }
                html += '</div></div>';
            });
            html += '</div>';
        }

        container.html(html);
    }

    // 获取状态徽章样式
    function getStatusBadgeClass(status) {
        switch(status) {
            case '已执行': return 'success';
            case '已发布': return 'primary';
            case '计划中': return 'info';
            default: return 'secondary';
        }
    }

    // 显示菜单错误
    function showMenuError(message) {
        ['#breakfast-menu', '#lunch-menu', '#dinner-menu'].forEach(function(selector) {
            $(selector).html('<div class="text-center py-4"><p class="text-danger">' + message + '</p></div>');
        });
    }

    // 绑定功能图标点击事件
    function bindFunctionCardClicks() {
        // 陪餐记录入口
        $('.companion-entry-link').click(function(e) {
            e.preventDefault();
            window.location.href = "{{ url_for('daily_management.index') }}";
        });

        // 培训记录入口
        $('.training-entry-link').click(function(e) {
            e.preventDefault();
            window.location.href = "{{ url_for('daily_management.index') }}";
        });

        // 特殊事件入口
        $('.event-entry-link').click(function(e) {
            e.preventDefault();
            window.location.href = "{{ url_for('daily_management.index') }}";
        });

        // 问题记录入口
        $('.issue-entry-link').click(function(e) {
            e.preventDefault();
            window.location.href = "{{ url_for('daily_management.index') }}";
        });
    }

    // 测试API函数
    function testAPIs() {
        console.log('开始测试API...');

        // 测试陪餐记录数据
        $.ajax({
            url: '/api/v2/test/companions',
            type: 'GET',
            success: function(data) {
                console.log('陪餐记录测试结果:', data);
                alert('陪餐记录测试成功！总数: ' + data.total_count + ' 条');
            },
            error: function(xhr, status, error) {
                console.error('陪餐记录测试失败:', status, error);
                alert('陪餐记录测试失败: ' + error);
            }
        });

        // 测试菜单数据
        $.ajax({
            url: '/api/v2/test/menu',
            type: 'GET',
            success: function(data) {
                console.log('菜单测试结果:', data);
                alert('菜单测试成功！总数: ' + data.total_count + ' 条');
            },
            error: function(xhr, status, error) {
                console.error('菜单测试失败:', status, error);
                alert('菜单测试失败: ' + error);
            }
        });
    }


</script>
{% endblock %}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>