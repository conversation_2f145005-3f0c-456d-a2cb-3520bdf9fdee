<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='bootstrap/css/bootstrap.min.css') }}">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}">
    <style nonce="{{ csp_nonce }}">
        body {
            background-color: #f8f9fa;
            padding-top: 20px;
            padding-bottom: 20px;
        }
        .container {
            max-width: 600px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            padding: 30px;
            text-align: center;
        }
        .success-icon {
            font-size: 80px;
            color: #1cc88a;
            margin-bottom: 20px;
        }
        .success-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .success-message {
            font-size: 18px;
            color: #666;
            margin-bottom: 30px;
        }
        .footer {
            margin-top: 30px;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="success-title">
            提交成功
        </div>
        <div class="success-message">
            您的陪餐记录已成功提交，感谢您的参与！
        </div>
        <div class="alert alert-info">
            <p>您的反馈对我们改进食堂服务质量非常重要</p>
            <p>{{ school_name }}食堂将根据您的建议不断提升服务水平</p>
        </div>
        <div class="mt-4">
            <button class="btn btn-primary" data-onclick="window.close()">关闭页面</button>
        </div>
        <div class="footer">
            <p>{{ school_name }}食堂管理系统</p>
        </div>
    </div>

    <!-- jQuery -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}"></script>
    <!-- Bootstrap JS -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='bootstrap/js/bootstrap.bundle.min.js') }}"></script>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
</body>
</html>
