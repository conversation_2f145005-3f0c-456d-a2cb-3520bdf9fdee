{% extends 'base.html' %}

{% block title %}消耗计划超级编辑器{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
  .batch-checkbox:checked + label {
    background-color: #007bff;
    color: white;
  }
  .highlight-row {
    background-color: #f8f9fa;
    transition: background-color 0.3s;
  }
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }
  .quantity-input {
    font-size: 1.2rem;
    font-weight: bold;
    color: #dc3545;
    text-align: center;
  }
  .price-display {
    font-size: 1.1rem;
    font-weight: bold;
    color: #dc3545;
  }
  .stock-quantity {
    font-size: 1.1rem;
    font-weight: bold;
    color: #28a745;
  }
  .remaining-quantity {
    font-size: 0.9rem;
    font-weight: bold;
    color: #dc3545;
    margin-top: 3px;
  }
  .expiry-warning {
    color: #ffffff;
    font-weight: bold;
    background-color: #dc3545;
    padding: 2px 5px;
    border-radius: 3px;
  }
  .expiry-alert {
    color: #ffffff;
    font-weight: bold;
    background-color: #fd7e14;
    padding: 2px 5px;
    border-radius: 3px;
  }
  .category-header {
    background-color: #f8f9fa;
    font-weight: bold;
    padding: 8px;
    margin-top: 10px;
    border-radius: 4px;
  }
  .search-box {
    margin-bottom: 15px;
  }
  .batch-info {
    font-size: 0.85rem;
    color: #6c757d;
  }
  .action-buttons {
    margin: 15px 0;
  }
  .table th {
    background-color: #f2f2f2;
    position: sticky;
    top: 0;
    z-index: 10;
  }

  /* 餐次多选框样式 */
  .meal-type-checkboxes {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .meal-type-checkbox:checked + label {
    background-color: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  /* 食谱分析样式 */
  .recipe-item {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 10px;
    background-color: #f8f9fa;
  }

  .recipe-title {
    font-weight: bold;
    color: #495057;
    margin-bottom: 5px;
  }

  .ingredient-list {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
  }

  .ingredient-tag {
    background-color: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.85rem;
  }

  .ingredient-tag.available {
    background-color: #d4edda;
    color: #155724;
  }

  .ingredient-tag.missing {
    background-color: #f8d7da;
    color: #721c24;
  }

  .missing-ingredients-alert {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 10px;
    margin-top: 10px;
  }

  /* 简单响应式优化 */
  .action-buttons .btn {
    margin-right: 8px;
    margin-bottom: 8px;
  }

  /* 移动端优化 */
  @media (max-width: 768px) {
    .action-buttons {
      text-align: left !important;
    }

    .action-buttons .btn {
      display: block;
      width: 100%;
      margin-right: 0;
      margin-bottom: 10px;
    }

    /* 表格移动端优化 */
    .table-responsive {
      max-height: 400px !important;
    }

    .table th,
    .table td {
      padding: 8px 4px;
      font-size: 12px;
    }

    /* 隐藏部分列在移动端 */
    .table th:nth-child(3),
    .table td:nth-child(3),
    .table th:nth-child(4),
    .table td:nth-child(4),
    .table th:nth-child(8),
    .table td:nth-child(8) {
      display: none;
    }

    /* 餐次选择优化 */
    .meal-type-checkboxes {
      flex-direction: column;
      gap: 8px;
    }

    .custom-control-inline {
      margin-right: 0;
      margin-bottom: 8px;
    }
  }

  /* 超小屏幕优化 */
  @media (max-width: 480px) {
    .table-responsive {
      max-height: 300px !important;
    }

    /* 进一步简化表格 */
    .table th:nth-child(5),
    .table td:nth-child(5) {
      display: none;
    }

    .table th,
    .table td {
      padding: 6px 2px;
      font-size: 11px;
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
          <h6 class="m-0 font-weight-bold text-primary">消耗计划超级编辑器</h6>
          <div>
            <a href="http://127.0.0.1:5000/consumption-plan" class="btn btn-secondary btn-sm">
              <i class="fas fa-arrow-left"></i> 返回列表
            </a>
          </div>
        </div>

        <div class="card-body">
          <!-- 消息容器 -->
          <div id="alertContainer"></div>

          <form id="consumptionForm" method="post" action="http://127.0.0.1:5000/consumption-plan/create_super">
            <!-- 基本信息部分 -->
            <div class="row mb-4">
              <div class="col-lg-3 col-md-6 col-12">
                <div class="form-group">
                  <label for="area_id">区域</label>
                  <select name="area_id" id="area_id" class="form-control" required>
                    <option value="">请选择区域</option>
                    {% for area in areas %}
                    <option value="{{ area.id }}">{{ area.name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <!-- 隐藏仓库字段，自动获取学校仓库 -->
              <input type="hidden" name="warehouse_id" id="warehouse_id" value="">
              <div class="col-lg-3 col-md-6 col-12">
                <div class="form-group">
                  <label for="consumption_date">消耗日期</label>
                  <input type="date" name="consumption_date" id="consumption_date" class="form-control" value="{{ today_date }}" required>
                </div>
              </div>
              <div class="col-lg-4 col-md-8 col-12">
                <div class="form-group">
                  <label for="meal_types">餐次 <small class="text-muted">(可多选)</small></label>
                  <div class="meal-type-checkboxes">
                    <div class="custom-control custom-checkbox custom-control-inline">
                      <input type="checkbox" class="custom-control-input meal-type-checkbox" id="meal_breakfast" name="meal_types[]" value="早餐">
                      <label class="custom-control-label" for="meal_breakfast">早餐</label>
                    </div>
                    <div class="custom-control custom-checkbox custom-control-inline">
                      <input type="checkbox" class="custom-control-input meal-type-checkbox" id="meal_lunch" name="meal_types[]" value="午餐">
                      <label class="custom-control-label" for="meal_lunch">午餐</label>
                    </div>
                    <div class="custom-control custom-checkbox custom-control-inline">
                      <input type="checkbox" class="custom-control-input meal-type-checkbox" id="meal_dinner" name="meal_types[]" value="晚餐">
                      <label class="custom-control-label" for="meal_dinner">晚餐</label>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-lg-2 col-md-4 col-12">
                <div class="form-group">
                  <label for="diners_count">用餐人数</label>
                  <input type="number" name="diners_count" id="diners_count" class="form-control" min="1" value="1">
                </div>
              </div>
            </div>

            <!-- 食谱分析和食材需求提示 -->
            <div class="row mb-4" id="recipeAnalysisSection" style="display: none;">
              <div class="col-12">
                <div class="card border-info">
                  <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-utensils mr-2"></i>食谱分析与食材需求</h6>
                  </div>
                  <div class="card-body">
                    <div id="recipeAnalysisContent">
                      <!-- 动态加载食谱分析内容 -->
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 搜索和批量操作 -->
            <div class="row mb-3">
              <div class="col-lg-4 col-md-6 col-12 mb-2">
                <div class="input-group search-box">
                  <input type="text" id="searchInput" class="form-control" placeholder="搜索食材...">
                  <div class="input-group-append">
                    <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                      <i class="fas fa-search"></i>
                    </button>
                  </div>
                </div>
              </div>
              <div class="col-lg-8 col-md-6 col-12">
                <div class="action-buttons">
                  <button type="button" class="btn btn-outline-primary btn-sm" id="selectAllBtn">
                    <i class="fas fa-check-square"></i> 全选
                  </button>
                  <button type="button" class="btn btn-outline-secondary btn-sm" id="deselectAllBtn">
                    <i class="fas fa-square"></i> 全不选
                  </button>
                  <button type="button" class="btn btn-outline-success btn-sm" id="fillAllBtn">
                    <i class="fas fa-fill"></i> 填充所有
                  </button>
                </div>
              </div>
            </div>

            <!-- 食材列表 -->
            <div class="position-relative">
              <div id="loading-overlay" class="loading-overlay">
                <div class="spinner-border text-primary" role="status">
                  <span class="sr-only">加载中...</span>
                </div>
              </div>

              <div class="table-responsive" style="max-height: 600px; overflow-y: auto;">
                <table class="table table-bordered table-hover" id="inventoryTable">
                  <thead>
                    <tr>
                      <th style="width: 5%">选择</th>
                      <th class="w-15">食材名称</th>
                      <th class="w-15">批次号/供应商</th>
                      <th style="width: 10%">入库日期</th>
                      <th style="width: 10%">保质期剩余天数</th>
                      <th style="width: 10%">库存数量</th>
                      <th style="width: 10%">单位</th>
                      <th style="width: 10%">单价</th>
                      <th class="w-15">消耗数量</th>
                    </tr>
                  </thead>
                  <tbody id="inventoryBody">
                    <tr>
                      <td colspan="9" class="text-center">请选择区域和仓库</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- 备注 -->
            <div class="form-group mt-4">
              <label for="notes">备注</label>
              <textarea name="notes" id="notes" class="form-control" rows="2"></textarea>
            </div>

            <!-- 提交按钮 -->
            <div class="text-center mt-4">
              <button type="submit" class="btn btn-primary btn-lg" id="saveBtn">
                <i class="fas fa-save"></i> 保存消耗计划
              </button>
              <a href="http://127.0.0.1:5000/consumption-plan" class="btn btn-secondary btn-lg">
                <i class="fas fa-times"></i> 取消
              </a>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
  $(document).ready(function() {
    // 初始化隐藏加载遮罩
    $('#loading-overlay').hide();

    // 区域选择变化时自动获取学校仓库
    $('#area_id').change(function() {
      var areaId = $(this).val();
      if (!areaId) {
        $('#warehouse_id').val('');
        $('#recipeAnalysisSection').hide();
        $('#inventoryBody').html('<tr><td colspan="9" class="text-center">请选择区域</td></tr>');
        return;
      }

      $('#loading-overlay').show();

      $.getJSON('http://127.0.0.1:5000/consumption-plan/get-warehouses/' + areaId)
        .done(function(data) {
          if (data.warehouses && data.warehouses.length > 0) {
            // 自动选择第一个（通常也是唯一的）仓库
            var warehouse = data.warehouses[0];
            $('#warehouse_id').val(warehouse.id);

            // 自动加载库存和分析食谱
            loadInventory();
            analyzeRecipes();
          } else {
            $('#warehouse_id').val('');
            alert('该学校还没有设置仓库，请先创建仓库');
          }
        })
        .fail(function(jqXHR, textStatus, errorThrown) {
          console.error('获取仓库失败:', jqXHR.responseJSON || errorThrown);
          alert('获取仓库信息失败');
        })
        .always(function() {
          $('#loading-overlay').hide();
        });
    });

    // 仓库字段已隐藏，不需要监听变化

    // 餐次选择变化时分析食谱
    $('.meal-type-checkbox').change(function() {
      analyzeRecipes();
    });

    // 消耗日期变化时分析食谱
    $('#consumption_date').change(function() {
      analyzeRecipes();
    });

    // 分析食谱和食材需求
    function analyzeRecipes() {
      var areaId = $('#area_id').val();
      var consumptionDate = $('#consumption_date').val();
      var selectedMealTypes = [];

      $('.meal-type-checkbox:checked').each(function() {
        selectedMealTypes.push($(this).val());
      });

      console.log('分析食谱参数:', {
        areaId: areaId,
        consumptionDate: consumptionDate,
        selectedMealTypes: selectedMealTypes
      });

      if (!areaId || !consumptionDate || selectedMealTypes.length === 0) {
        $('#recipeAnalysisSection').hide();
        return;
      }

      // 显示分析区域
      $('#recipeAnalysisSection').show();
      $('#recipeAnalysisContent').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> 分析食谱中...</div>');

      // 获取食谱分析
      $.ajax({
        url: '/consumption-plan/analyze-recipes',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
          area_id: parseInt(areaId),
          consumption_date: consumptionDate,
          meal_types: selectedMealTypes
        }),
        success: function(response) {
          console.log('食谱分析响应:', response);
          displayRecipeAnalysis(response);
        },
        error: function(xhr, status, error) {
          console.error('食谱分析失败:', xhr.responseText);
          var errorMsg = '无法获取食谱分析信息';
          if (xhr.responseJSON && xhr.responseJSON.error) {
            errorMsg = xhr.responseJSON.error;
          }
          $('#recipeAnalysisContent').html('<div class="alert alert-warning">' + errorMsg + '</div>');
        }
      });
    }

    // 显示食谱分析结果
    function displayRecipeAnalysis(data) {
      var html = '';

      if (data.recipes && data.recipes.length > 0) {
        html += '<div class="row">';

        data.recipes.forEach(function(recipe) {
          html += '<div class="col-md-6 mb-3">';
          html += '<div class="recipe-item">';
          html += '<div class="recipe-title">' + recipe.meal_type + ' - ' + recipe.recipe_name + '</div>';

          if (recipe.main_ingredients && recipe.main_ingredients.length > 0) {
            html += '<div class="ingredient-list">';
            recipe.main_ingredients.forEach(function(ingredient) {
              var tagClass = ingredient.available ? 'available' : 'missing';
              var icon = ingredient.available ? '✓' : '✗';
              html += '<span class="ingredient-tag ' + tagClass + '">' + icon + ' ' + ingredient.name;
              if (ingredient.required_quantity) {
                html += ' (' + ingredient.required_quantity + ingredient.unit + ')';
              }
              html += '</span>';
            });
            html += '</div>';
          }

          html += '</div>';
          html += '</div>';
        });

        html += '</div>';

        // 显示缺少的食材汇总
        if (data.missing_ingredients && data.missing_ingredients.length > 0) {
          html += '<div class="missing-ingredients-alert">';
          html += '<h6><i class="fas fa-exclamation-triangle text-warning"></i> 需要采购的食材：</h6>';
          html += '<div class="ingredient-list">';
          data.missing_ingredients.forEach(function(ingredient) {
            html += '<span class="ingredient-tag missing">' + ingredient.name;
            if (ingredient.total_required) {
              html += ' (共需 ' + ingredient.total_required + ingredient.unit + ')';
            }
            html += '</span>';
          });
          html += '</div>';
          html += '<div class="mt-3">';
          html += '<a href="/purchase-order/new" class="btn btn-warning btn-sm" target="_blank">';
          html += '<i class="fas fa-shopping-cart"></i> 创建采购订单';
          html += '</a>';
          html += '</div>';
          html += '</div>';
        } else {
          html += '<div class="alert alert-success">';
          html += '<i class="fas fa-check-circle"></i> 所有食材库存充足，可以正常制作！';
          html += '</div>';
        }

      } else {
        html = '<div class="alert alert-info">所选日期和餐次暂无食谱安排</div>';
      }

      $('#recipeAnalysisContent').html(html);
    }

    // 采购订单创建功能已简化为直接链接，无需JavaScript函数

    // 加载库存数据
    function loadInventory() {
      var warehouseId = $('#warehouse_id').val();
      if (!warehouseId) {
        $('#inventoryBody').html('<tr><td colspan="9" class="text-center">请选择仓库</td></tr>');
        return;
      }

      $('#loading-overlay').show();

      $.getJSON('http://127.0.0.1:5000/consumption-plan/get_inventory_batches?warehouse_id=' + warehouseId)
        .done(function(data) {
          $('#inventoryBody').empty();

          if (!data || data.length === 0) {
            $('#inventoryBody').html('<tr><td colspan="9" class="text-center">该仓库暂无库存</td></tr>');
            return;
          }

          // 按分类分组显示
          var currentCategory = '';

          $.each(data, function(index, item) {
            // 添加分类标题
            if (item.category !== currentCategory) {
              currentCategory = item.category;
              $('#inventoryBody').append(`
                <tr>
                  <td colspan="9" class="category-header">${currentCategory || '未分类'}</td>
                </tr>
              `);
            }

            // 计算是否临期
            var today = new Date();
            var expiryDate = new Date(item.expiry_date);
            var daysDiff = Math.floor((expiryDate - today) / (1000 * 60 * 60 * 24));

            var expiryClass = '';
            var daysText = '';
            if (daysDiff <= 0) {
              expiryClass = 'expiry-warning';
              daysText = '已过期';
            } else if (daysDiff <= 3) {
              expiryClass = 'expiry-alert';
              daysText = daysDiff + ' 天（临期）';
            } else {
              daysText = daysDiff + ' 天';
            }

            // 添加库存行
            $('#inventoryBody').append(`
              <tr data-ingredient-id="${item.ingredient_id}" data-batch-id="${item.id}">
                <td class="text-center">
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input batch-checkbox"
                           id="batch_${item.id}" name="selected_batches[]" value="${item.id}">
                    <label class="custom-control-label" for="batch_${item.id}"></label>
                  </div>
                </td>
                <td>${item.ingredient_name}</td>
                <td>${item.batch_number}<br><small class="text-muted">${item.supplier_name || '-'}</small></td>
                <td>${item.production_date}</td>
                <td class="${expiryClass}">${daysText}</td>
                <td>
                  <div class="stock-quantity">${item.quantity}</div>
                  <div class="remaining-quantity text-danger" style="display:none;"></div>
                </td>
                <td>${item.unit}</td>
                <td class="price-display">${item.unit_price ? item.unit_price.toFixed(2) : '0.00'}</td>
                <td>
                  <input type="number" name="quantity_${item.id}" class="form-control quantity-input"
                         min="0" max="${item.quantity}" step="0.01" disabled>
                </td>
              </tr>
            `);
          });

          // 绑定事件
          bindEvents();
        })
        .fail(function(jqXHR, textStatus, errorThrown) {
          console.error('获取库存失败:', jqXHR.responseJSON || errorThrown);
          $('#inventoryBody').html(`
            <tr>
              <td colspan="9" class="text-center text-danger">
                获取库存失败: ${jqXHR.responseJSON ? jqXHR.responseJSON.message : errorThrown}
              </td>
            </tr>
          `);
        })
        .always(function() {
          $('#loading-overlay').hide();
        });
    }

    // 绑定事件
    function bindEvents() {
      // 勾选/取消勾选批次
      $('.batch-checkbox').change(function() {
        var batchId = $(this).val();
        var quantityInput = $('input[name="quantity_' + batchId + '"]');
        var row = $(this).closest('tr');
        var remainingQuantityDiv = row.find('.remaining-quantity');

        if ($(this).is(':checked')) {
          quantityInput.prop('disabled', false).focus();
        } else {
          quantityInput.prop('disabled', true).val('');
          remainingQuantityDiv.hide();
        }
      });

      // 监听消耗数量输入变化
      $('.quantity-input').on('input', function() {
        var batchId = $(this).attr('name').replace('quantity_', '');
        var row = $(this).closest('tr');
        var stockQuantity = parseFloat(row.find('.stock-quantity').text());
        var consumptionQuantity = parseFloat($(this).val()) || 0;

        // 确保消耗数量不超过库存数量
        if (consumptionQuantity > stockQuantity) {
          consumptionQuantity = stockQuantity;
          $(this).val(stockQuantity);
          alert('消耗数量不能超过库存数量！');
        }

        var remainingQuantity = stockQuantity - consumptionQuantity;
        var remainingQuantityDiv = row.find('.remaining-quantity');

        if (consumptionQuantity > 0) {
          remainingQuantityDiv.text('剩余: ' + remainingQuantity.toFixed(2)).show();

          // 如果剩余量为负数，添加警告样式
          if (remainingQuantity < 0) {
            remainingQuantityDiv.addClass('text-danger font-weight-bold');
          } else {
            remainingQuantityDiv.removeClass('text-danger font-weight-bold');
          }
        } else {
          remainingQuantityDiv.hide();
        }
      });
    }

    // 全选按钮
    $('#selectAllBtn').click(function() {
      $('.batch-checkbox').prop('checked', true).trigger('change');
    });

    // 全不选按钮
    $('#deselectAllBtn').click(function() {
      $('.batch-checkbox').prop('checked', false).trigger('change');
    });

    // 填充所有按钮
    $('#fillAllBtn').click(function() {
      $('.batch-checkbox:checked').each(function() {
        var batchId = $(this).val();
        var row = $(this).closest('tr');
        var maxQuantity = parseFloat(row.find('.stock-quantity').text());
        var quantityInput = $('input[name="quantity_' + batchId + '"]');
        quantityInput.val(maxQuantity).trigger('input');
      });
    });

    // 搜索功能
    $('#searchBtn').click(function() {
      var searchText = $('#searchInput').val().toLowerCase();
      $('#inventoryTable tbody tr').each(function() {
        var row = $(this);
        if (row.find('.category-header').length > 0) {
          // 这是分类行，始终显示
          row.show();
        } else {
          var ingredientName = row.find('td:eq(1)').text().toLowerCase();
          var batchAndSupplier = row.find('td:eq(2)').text().toLowerCase();

          if (ingredientName.includes(searchText) ||
              batchAndSupplier.includes(searchText)) {
            row.show();
          } else {
            row.hide();
          }
        }
      });
    });

    // 回车键触发搜索
    $('#searchInput').keypress(function(e) {
      if (e.which === 13) {
        $('#searchBtn').click();
        e.preventDefault();
      }
    });

    // 表单提交验证
    $('#consumptionForm').submit(function(e) {
      var hasSelectedBatches = $('.batch-checkbox:checked').length > 0;

      if (!hasSelectedBatches) {
        e.preventDefault();
        alert('请至少选择一个批次进行消耗');
        return false;
      }

      var hasQuantity = false;
      $('.batch-checkbox:checked').each(function() {
        var batchId = $(this).val();
        var quantity = $('input[name="quantity_' + batchId + '"]').val();

        if (quantity && parseFloat(quantity) > 0) {
          hasQuantity = true;
          return false; // 跳出循环
        }
      });

      if (!hasQuantity) {
        e.preventDefault();
        alert('请至少为一个选中的批次设置消耗数量');
        return false;
      }

      return true;
    });

    // 页面加载时自动选择第一个区域
    if ($('#area_id option').length > 1) {
      $('#area_id').val($('#area_id option:eq(1)').val()).trigger('change');
    }
  });
</script>
{% endblock %}
