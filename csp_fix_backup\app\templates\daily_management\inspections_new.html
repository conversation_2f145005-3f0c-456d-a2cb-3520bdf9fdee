{% extends 'base.html' %}

{% block title %}检查记录{% endblock %}

{% from 'daily_management/components/enhanced_image_uploader.html' import enhanced_image_uploader %}

{% block content %}
<div class="container-fluid">
    <!-- 库存管理流程引导 -->
    <div class="card mb-4 border-primary">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-clipboard-check"></i> 日常检查 - 流程指引</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle" style="font-size: 1.2rem; margin-right: 10px;"></i> <strong>提示：</strong> 日常检查是食堂管理的重要环节，通过定期检查可以及时发现并解决问题，确保食品安全和卫生。
            </div>

            <div class="workflow-context mt-3">
                <div class="previous-step">
                    <small class="text-muted">上一步</small>
                    <p><i class="fas fa-calendar-day"></i> 日志管理</p>
                    <small>记录每日工作情况</small>
                    <div class="mt-2">
                        <a href="{{ url_for('daily_management.edit_log', date_str=log.log_date|safe_datetime('%Y-%m-%d')) }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回日志
                        </a>
                    </div>
                </div>
                <div class="current-step bg-light p-2 border rounded">
                    <small class="text-muted">当前步骤</small>
                    <p class="font-weight-bold"><i class="fas fa-clipboard-check"></i> 日常检查</p>
                    <small>记录晨检、午检、晚检情况</small>
                </div>
                <div class="next-step">
                    <small class="text-muted">下一步</small>
                    <p><i class="fas fa-utensils"></i> 陪餐记录</p>
                    <small>记录陪餐情况</small>
                    <div class="mt-2">
                        <a href="{{ url_for('daily_management.companions', log_id=log.id) }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-arrow-right"></i> 前往陪餐记录
                        </a>
                    </div>
                </div>
            </div>

            <!-- 操作提示 -->
            <div class="alert alert-light border mt-3">
                <h6 class="alert-heading"><i class="fas fa-lightbulb text-warning"></i> 操作提示</h6>
                <ul class="mb-0">
                    <li>每天应进行三次检查：晨检、午检和晚检</li>
                    <li>检查内容包括：地面卫生、操作台卫生、设备卫生、食材存储、人员卫生、餐具消毒等</li>
                    <li>发现问题应及时记录并拍照，以便后续跟进处理</li>
                    <li>可以使用左右箭头键快速切换日期，按D键打开日期选择器</li>
                    <li>检查记录将作为食品安全管理的重要依据，请认真填写</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 日期导航 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2>{{ log.log_date|safe_datetime('%Y-%m-%d') }} 检查记录</h2>
        </div>
        <div class="col-md-4 text-right">
            <div class="btn-group">
                {% if prev_log %}
                <a href="{{ url_for('daily_management.inspections', log_id=prev_log.id) }}" class="btn btn-outline-primary">
                    <i class="fas fa-chevron-left"></i> 前一天
                </a>
                {% else %}
                <button class="btn btn-outline-primary" disabled>
                    <i class="fas fa-chevron-left"></i> 前一天
                </button>
                {% endif %}

                <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-calendar-alt"></i> 选择日期
                </button>
                <div class="dropdown-menu">
                    <form class="px-3 py-2" onsubmit="goToDate(event)">
                        <div class="form-group mb-2">
                            <label for="dateInput">选择日期</label>
                            <input type="date" class="form-control" id="dateInput" value="{{ log.log_date|safe_datetime('%Y-%m-%d') }}">
                        </div>
                        <button type="submit" class="btn btn-primary btn-sm btn-block">
                            <i class="fas fa-search"></i> 查看
                        </button>
                    </form>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" href="{{ url_for('daily_management.inspections_by_date', date_str=today|safe_datetime('%Y-%m-%d')) }}">
                        <i class="fas fa-calendar-day"></i> 今天
                    </a>
                    <a class="dropdown-item" href="{{ url_for('daily_management.inspections_by_date', date_str=(today - timedelta(days=7))|safe_datetime('%Y-%m-%d')) }}">
                        <i class="fas fa-calendar-week"></i> 上周同一天
                    </a>
                </div>

                {% if next_log %}
                <a href="{{ url_for('daily_management.inspections', log_id=next_log.id) }}" class="btn btn-outline-primary">
                    后一天 <i class="fas fa-chevron-right"></i>
                </a>
                {% else %}
                <button class="btn btn-outline-primary" disabled>
                    后一天 <i class="fas fa-chevron-right"></i>
                </button>
                {% endif %}
            </div>

            <a href="{{ url_for('daily_management.print_inspections', log_id=log.id) }}" class="btn btn-success ml-2">
                <i class="fas fa-print"></i> 打印
            </a>
        </div>
    </div>

    <!-- 检查记录卡片 -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title"><i class="fas fa-sun text-warning"></i> 晨检记录</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='morning') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> 编辑晨检
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if morning_inspections %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th width="20%">检查项目</th>
                                    <th width="10%">状态</th>
                                    <th>详情</th>
                                    <th width="30%">照片</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for inspection in morning_inspections %}
                                <tr>
                                    <td>{{ inspection.inspection_item }}</td>
                                    <td class="text-center">
                                        {% if inspection.status == 'normal' %}
                                        <span class="badge badge-success">正常</span>
                                        {% else %}
                                        <span class="badge badge-danger">异常</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ inspection.description or '无描述' }}</td>
                                    <td>
                                        {% if inspection.photos %}
                                        <div class="d-flex flex-wrap">
                                            {% for photo in inspection.photos %}
                                            <img src="{{ photo.file_path }}"
                                                 style="width: 60px; height: 60px; object-fit: cover; margin: 2px; cursor: pointer; border-radius: 4px;"
                                                 data-toggle="tooltip"
                                                 title="点击查看大图"
                                                 onclick="showFullImage('{{ photo.file_path }}', {{ photo.rating or 0 }})">
                                            {% endfor %}
                                        </div>
                                        {% else %}
                                        <span class="text-muted">无照片</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <p class="text-muted">暂无晨检记录</p>
                        <a href="{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='morning') }}" class="btn btn-outline-primary">
                            <i class="fas fa-plus"></i> 添加晨检记录
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title"><i class="fas fa-sun text-primary"></i> 午检记录</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='noon') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> 编辑午检
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if noon_inspections %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th width="20%">检查项目</th>
                                    <th width="10%">状态</th>
                                    <th>详情</th>
                                    <th width="30%">照片</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for inspection in noon_inspections %}
                                <tr>
                                    <td>{{ inspection.inspection_item }}</td>
                                    <td class="text-center">
                                        {% if inspection.status == 'normal' %}
                                        <span class="badge badge-success">正常</span>
                                        {% else %}
                                        <span class="badge badge-danger">异常</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ inspection.description or '无描述' }}</td>
                                    <td>
                                        {% if inspection.photos %}
                                        <div class="d-flex flex-wrap">
                                            {% for photo in inspection.photos %}
                                            <img src="{{ photo.file_path }}"
                                                 style="width: 60px; height: 60px; object-fit: cover; margin: 2px; cursor: pointer; border-radius: 4px;"
                                                 data-toggle="tooltip"
                                                 title="点击查看大图"
                                                 onclick="showFullImage('{{ photo.file_path }}', {{ photo.rating or 0 }})">
                                            {% endfor %}
                                        </div>
                                        {% else %}
                                        <span class="text-muted">无照片</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <p class="text-muted">暂无午检记录</p>
                        <a href="{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='noon') }}" class="btn btn-outline-primary">
                            <i class="fas fa-plus"></i> 添加午检记录
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title"><i class="fas fa-moon text-info"></i> 晚检记录</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='evening') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> 编辑晚检
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if evening_inspections %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th width="20%">检查项目</th>
                                    <th width="10%">状态</th>
                                    <th>详情</th>
                                    <th width="30%">照片</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for inspection in evening_inspections %}
                                <tr>
                                    <td>{{ inspection.inspection_item }}</td>
                                    <td class="text-center">
                                        {% if inspection.status == 'normal' %}
                                        <span class="badge badge-success">正常</span>
                                        {% else %}
                                        <span class="badge badge-danger">异常</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ inspection.description or '无描述' }}</td>
                                    <td>
                                        {% if inspection.photos %}
                                        <div class="d-flex flex-wrap">
                                            {% for photo in inspection.photos %}
                                            <img src="{{ photo.file_path }}"
                                                 style="width: 60px; height: 60px; object-fit: cover; margin: 2px; cursor: pointer; border-radius: 4px;"
                                                 data-toggle="tooltip"
                                                 title="点击查看大图"
                                                 onclick="showFullImage('{{ photo.file_path }}', {{ photo.rating or 0 }})">
                                            {% endfor %}
                                        </div>
                                        {% else %}
                                        <span class="text-muted">无照片</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <p class="text-muted">暂无晚检记录</p>
                        <a href="{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='evening') }}" class="btn btn-outline-primary">
                            <i class="fas fa-plus"></i> 添加晚检记录
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 照片管理区域 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title"><i class="fas fa-camera"></i> 照片管理</h3>
                </div>
                <div class="card-body">
                    <ul class="nav nav-tabs" id="photoTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="morning-tab" data-toggle="tab" href="#morning-photos" role="tab" aria-controls="morning-photos" aria-selected="true">
                                <i class="fas fa-sun text-warning"></i> 晨检照片
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="noon-tab" data-toggle="tab" href="#noon-photos" role="tab" aria-controls="noon-photos" aria-selected="false">
                                <i class="fas fa-sun text-primary"></i> 午检照片
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="evening-tab" data-toggle="tab" href="#evening-photos" role="tab" aria-controls="evening-photos" aria-selected="false">
                                <i class="fas fa-moon text-info"></i> 晚检照片
                            </a>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="photoTabsContent">
                        <div class="tab-pane fade show active" id="morning-photos" role="tabpanel" aria-labelledby="morning-tab">
                            {{ enhanced_image_uploader('inspection_morning', log.id, title='晨检照片管理', max_files=10, max_file_size=5, show_rating=True) }}
                        </div>
                        <div class="tab-pane fade" id="noon-photos" role="tabpanel" aria-labelledby="noon-tab">
                            {{ enhanced_image_uploader('inspection_noon', log.id, title='午检照片管理', max_files=10, max_file_size=5, show_rating=True) }}
                        </div>
                        <div class="tab-pane fade" id="evening-photos" role="tabpanel" aria-labelledby="evening-tab">
                            {{ enhanced_image_uploader('inspection_evening', log.id, title='晚检照片管理', max_files=10, max_file_size=5, show_rating=True) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    // 初始化工具提示
    $(function () {
        $('[data-toggle="tooltip"]').tooltip();
    });

    // 显示大图
    function showFullImage(imageSrc, rating) {
        // 构建星级评分HTML
        let ratingHtml = '';
        for (let i = 1; i <= 5; i++) {
            if (i <= rating) {
                ratingHtml += '<i class="fas fa-star text-warning"></i>';
            } else {
                ratingHtml += '<i class="far fa-star text-warning"></i>';
            }
        }

        // 使用SweetAlert2显示大图
        Swal.fire({
            imageUrl: imageSrc,
            imageAlt: '检查照片',
            html: `
                <div class="mt-3">
                    <div class="rating mb-2">${ratingHtml}</div>
                </div>
            `,
            showCloseButton: true,
            showConfirmButton: false,
            width: 'auto',
            padding: '1rem',
            background: '#fff',
            backdrop: 'rgba(0,0,0,0.8)'
        });
    }

    // 跳转到指定日期
    function goToDate(event) {
        event.preventDefault();
        const dateInput = document.getElementById('dateInput');
        const selectedDate = dateInput.value;

        if (selectedDate) {
            window.location.href = "{{ url_for('daily_management.inspections_by_date', date_str='') }}" + selectedDate;
        }
    }

    // 键盘导航
    document.addEventListener('keydown', function(e) {
        // 左箭头键 - 前一天
        if (e.keyCode === 37) {
            {% if prev_log %}
            window.location.href = "{{ url_for('daily_management.simplified_inspection', log_id=prev_log.id) }}";
            {% endif %}
        }
        // 右箭头键 - 后一天
        else if (e.keyCode === 39) {
            {% if next_log %}
            window.location.href = "{{ url_for('daily_management.simplified_inspection', log_id=next_log.id) }}";
            {% endif %}
        }
    });
</script>
{% endblock %}
