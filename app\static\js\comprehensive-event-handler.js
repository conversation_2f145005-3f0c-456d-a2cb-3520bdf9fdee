/**
 * 全面的安全事件处理器
 * 处理所有类型的安全关键事件
 */


    // 安全执行代码的函数（替代eval）
    function safeExecuteCode(code) {
        if (!code) return;

        code = code.trim();

        // 处理页面跳转
        if (code.includes('window.location.href=') || code.includes('location.href=') || code.includes('window.location=')) {
            // 匹配带引号的URL
            let urlMatch = code.match(/(?:window\.)?location(?:\.href)?\s*=\s*['"]([^'"]+)['"]/);
            if (urlMatch) {
                window.location.href = urlMatch[1];
                return true;
            }

            // 匹配不带引号的URL（直接赋值）
            urlMatch = code.match(/(?:window\.)?location(?:\.href)?\s*=\s*([^;]+)/);
            if (urlMatch) {
                const url = urlMatch[1].trim();
                // 移除可能的引号
                const cleanUrl = url.replace(/^['"]|['"]$/g, '');
                window.location.href = cleanUrl;
                return true;
            }
        }

        // 处理confirm调用
        if (code.includes('confirm(')) {
            const confirmMatch = code.match(/confirm\s*\(\s*['"](.*?)['"]\s*\)/);
            if (confirmMatch) {
                return confirm(confirmMatch[1]);
            }
        }

        // 处理简单的函数调用
        const funcMatch = code.match(/^(\w+)\s*\(([^)]*)\)$/);
        if (funcMatch) {
            const funcName = funcMatch[1];
            if (typeof window[funcName] === 'function') {
                try {
                    return window[funcName]();
                } catch (e) {
                    console.warn('函数执行失败:', funcName, e);
                }
            }
        }

        // 处理其他安全操作
        if (code === 'window.print()') {
            window.print();
            return true;
        }
        if (code === 'history.back()') {
            history.back();
            return true;
        }
        if (code === 'location.reload()') {
            location.reload();
            return true;
        }

        console.warn('不支持的代码执行:', code);
        return false;
    }

    


    document.addEventListener('DOMContentLoaded', function() {
    
    // 处理安全确认操作
    document.querySelectorAll('[data-action="safe-confirm"]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            
            const confirmCode = this.getAttribute('data-confirm-code');
            
            try {
                // 安全执行确认代码
                const result = safeExecuteCode(confirmCode);
                console.log('确认操作执行结果:', result);
            } catch (error) {
                console.error('确认操作执行失败:', error);
                alert('操作失败，请重试');
            }
        });
    });
    
    // 处理安全删除操作
    document.querySelectorAll('[data-action="safe-delete"]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            
            const deleteCode = this.getAttribute('data-delete-code');
            const confirmMessage = this.getAttribute('data-confirm-message') || '确定要删除吗？';
            
            if (confirm(confirmMessage)) {
                try {
                    safeExecuteCode(deleteCode);
                } catch (error) {
                    console.error('删除操作执行失败:', error);
                    alert('删除失败，请重试');
                }
            }
        });
    });
    
    // 处理复杂确认操作
    document.querySelectorAll('[data-action="complex-confirm"]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            
            const complexCode = this.getAttribute('data-complex-code');
            
            try {
                // 对于复杂的确认逻辑，直接执行
                safeExecuteCode(complexCode);
            } catch (error) {
                console.error('复杂确认操作执行失败:', error);
                alert('操作失败，请重试');
            }
        });
    });
    
    // 处理安全提交操作
    document.querySelectorAll('[data-action="safe-submit"]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            
            const submitCode = this.getAttribute('data-submit-code');
            
            try {
                safeExecuteCode(submitCode);
            } catch (error) {
                console.error('提交操作执行失败:', error);
                alert('提交失败，请检查输入');
            }
        });
    });
    
    // 处理安全导航操作
    document.querySelectorAll('[data-action="safe-navigate"]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            
            const navigateCode = this.getAttribute('data-navigate-code');
            
            try {
                safeExecuteCode(navigateCode);
            } catch (error) {
                console.error('导航操作执行失败:', error);
                alert('页面跳转失败');
            }
        });
    });
    
    // 处理通用安全执行
    document.querySelectorAll('[data-action="safe-execute"]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            
            const executeCode = this.getAttribute('data-execute-code');
            
            try {
                safeExecuteCode(executeCode);
            } catch (error) {
                console.error('代码执行失败:', error);
            }
        });
    });
    
    // 兼容之前的所有修复
    bindLegacyHandlers();
    

    
    // 绑定之前的处理器（兼容性）
    function bindLegacyHandlers() {
        // 处理之前修复的各种类型
        const legacySelectors = [
            '[data-action="critical-confirm"]',
            '[data-action="delete-confirm"]',
            '[data-validation="critical"]',
            '[data-validation="true"]',
            '.print-button',
            '.back-button',
            '.reload-button'
        ];
        
        legacySelectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(element => {
                if (!element.hasAttribute('data-legacy-bound')) {
                    bindLegacyElement(element, selector);
                    element.setAttribute('data-legacy-bound', 'true');
                }
            });
        });
    }
    
    function bindLegacyElement(element, selector) {
        if (selector.includes('print-button')) {
            element.addEventListener('click', () => window.print());
        } else if (selector.includes('back-button')) {
            element.addEventListener('click', () => history.back());
        } else if (selector.includes('reload-button')) {
            element.addEventListener('click', () => location.reload());
        }
        // 其他类型的处理器已经在之前的脚本中定义
    }
    
    console.log('✅ 全面的安全事件处理器已加载');
});