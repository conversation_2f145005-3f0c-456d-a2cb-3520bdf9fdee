{% extends 'base.html' %}

{% block title %}检查记录{% endblock %}

{% from 'daily_management/components/enhanced_image_uploader.html' import enhanced_image_uploader %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    /* 全局样式优化 */
    body {
        background-color: #f8f9fc;
    }

    .container-fluid {
        padding: 1.5rem;
        animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* 日期导航样式增强 */
    .date-navigation {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 2rem;
        background: linear-gradient(135deg, #f0f5ff, #ffffff);
        border-radius: 1rem;
        padding: 1.25rem 1.75rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.05);
        position: relative;
        overflow: hidden;
    }

    .date-navigation::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, #4e73df, #36b9cc);
    }

    .date-navigation .date-display {
        font-size: 1.75rem;
        font-weight: 700;
        color: #3a3b45;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        position: relative;
    }

    .date-navigation .date-display::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 0;
        width: 40px;
        height: 3px;
        background: #4e73df;
        border-radius: 3px;
    }

    .date-navigation .nav-buttons {
        display: flex;
        gap: 0.75rem;
    }

    .date-navigation .nav-btn {
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        display: flex;
        align-items: center;
        gap: 0.375rem;
        box-shadow: 0 2px 5px rgba(0,0,0,0.08);
        border: none;
    }

    .date-navigation .nav-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .date-navigation .nav-btn.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    /* 检查项卡片样式增强 */
    .inspection-card {
        height: 100%;
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        border-radius: 1rem;
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        margin-bottom: 2rem;
        overflow: hidden;
        position: relative;
    }

    .inspection-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .inspection-card .card-header {
        background: linear-gradient(135deg, #f8f9fc, #ffffff);
        border-bottom: 1px solid rgba(227, 230, 240, 0.5);
        border-radius: 1rem 1rem 0 0;
        padding: 1.25rem 1.5rem;
        position: relative;
    }

    .inspection-card .card-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: #4e73df;
        border-radius: 4px 0 0 0;
    }

    .inspection-card .card-header h6 {
        font-size: 1.1rem;
        letter-spacing: 0.5px;
    }

    .inspection-card .card-body {
        padding: 1.5rem;
        background-color: #ffffff;
    }

    /* 检查项表格样式增强 */
    .inspection-table {
        border-collapse: separate;
        border-spacing: 0;
        width: 100%;
        border: 1px solid #e3e6f0;
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .inspection-table th {
        background: linear-gradient(135deg, #f0f5ff, #f8f9fc);
        font-weight: 600;
        padding: 1rem 1.25rem;
        border-bottom: 2px solid #e3e6f0;
        color: #3a3b45;
        font-size: 0.95rem;
        letter-spacing: 0.5px;
        text-transform: uppercase;
    }

    .inspection-table td {
        vertical-align: middle;
        padding: 1.25rem;
        border-bottom: 1px solid #f0f0f0;
        transition: background-color 0.2s;
    }

    .inspection-table tr:last-child td {
        border-bottom: none;
    }

    .inspection-table tr:hover td {
        background-color: #f8f9fc;
    }

    /* 照片展示样式增强 */
    .photo-gallery {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
        gap: 0.875rem;
        margin-top: 1rem;
    }

    .photo-thumbnail {
        width: 90px;
        height: 90px;
        object-fit: cover;
        border-radius: 0.5rem;
        border: 2px solid #f0f0f0;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        position: relative;
    }

    .photo-thumbnail:hover {
        transform: scale(1.08);
        border-color: #4e73df;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        z-index: 1;
    }

    .photo-thumbnail::after {
        content: '👁️';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0);
        background-color: rgba(0,0,0,0.5);
        color: white;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.5rem;
        opacity: 0;
        transition: all 0.3s;
    }

    .photo-thumbnail:hover::after {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }

    /* 评分星星样式增强 */
    .rating {
        color: #f6c23e;
        font-size: 1rem;
        display: flex;
        gap: 0.25rem;
        justify-content: center;
    }

    /* 状态标签样式增强 */
    .badge {
        padding: 0.5em 1em;
        font-weight: 600;
        border-radius: 30px;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        display: inline-flex;
        align-items: center;
        gap: 0.375rem;
        transition: all 0.3s;
    }

    .badge-success {
        background: linear-gradient(135deg, #e6f7ee, #d1f2e0);
        color: #1f9d55;
        border: none;
    }

    .badge-danger {
        background: linear-gradient(135deg, #fde8e8, #fad1d1);
        color: #e53e3e;
        border: none;
    }

    .badge i {
        font-size: 0.75rem;
    }

    /* 按钮样式增强 */
    .btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 600;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        box-shadow: 0 2px 5px rgba(0,0,0,0.08);
        border: none;
        letter-spacing: 0.3px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #4e73df, #3a5fc8);
    }

    .btn-info {
        background: linear-gradient(135deg, #36b9cc, #2a9aad);
    }

    .btn-light {
        background: linear-gradient(135deg, #f8f9fc, #e9ecef);
        color: #5a5c69;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .btn i {
        font-size: 0.875rem;
    }

    /* 空状态样式增强 */
    .text-center.py-4 {
        padding: 2.5rem 1rem;
        background-color: #f8f9fc;
        border-radius: 0.5rem;
        transition: all 0.3s;
    }

    .text-center.py-4:hover {
        background-color: #f0f5ff;
        box-shadow: 0 3px 10px rgba(0,0,0,0.05);
    }

    .text-center.py-4 i {
        color: #d1d3e2;
        transition: all 0.3s;
    }

    .text-center.py-4:hover i {
        color: #4e73df;
        transform: scale(1.1);
    }

    /* 照片管理区域样式 */
    .photo-management-section {
        margin-top: 1rem;
        padding-top: 2rem;
        border-top: 1px dashed #e3e6f0;
    }

    /* 下拉菜单样式增强 */
    .dropdown-menu {
        border: none;
        border-radius: 0.5rem;
        box-shadow: 0 5px 25px rgba(0,0,0,0.1);
        padding: 1rem;
        animation: fadeInDown 0.3s;
    }

    @keyframes fadeInDown {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .dropdown-item {
        border-radius: 0.375rem;
        padding: 0.75rem 1rem;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .dropdown-item:hover {
        background-color: #f0f5ff;
        transform: translateX(3px);
    }

    /* 响应式调整增强 */
    @media (max-width: 992px) {
        .date-navigation {
            padding: 1.25rem;
        }

        .date-navigation .date-display {
            font-size: 1.5rem;
        }
    }

    @media (max-width: 768px) {
        .date-navigation {
            flex-direction: column;
            gap: 1.25rem;
            padding: 1.25rem;
        }

        .date-navigation .date-display {
            font-size: 1.35rem;
            text-align: center;
            justify-content: center;
        }

        .date-navigation .date-display::after {
            left: 50%;
            transform: translateX(-50%);
        }

        .date-navigation .nav-buttons {
            flex-wrap: wrap;
            justify-content: center;
            gap: 0.625rem;
        }

        .nav-btn {
            flex: 1;
            min-width: 120px;
            justify-content: center;
        }

        .inspection-card {
            margin-bottom: 1.5rem;
        }
    }

    /* 加载动画 */
    .loading-spinner {
        display: inline-block;
        width: 1.5rem;
        height: 1.5rem;
        border: 3px solid rgba(255,255,255,0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    /* 图片查看器增强 */
    .swal2-popup {
        border-radius: 1rem;
        padding: 0;
        overflow: hidden;
    }

    .swal2-image {
        margin: 0;
        border-radius: 0;
        max-height: 70vh;
        object-fit: contain;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 导入导航宏 -->
    {% from 'daily_management/components/navigation.html' import daily_management_header %}

    <!-- 显示导航和学校信息 -->
    {{ daily_management_header(title, school, log, 'inspections') }}

    <!-- 日期导航 -->
    <div class="date-navigation">
        <h1 class="date-display">检查记录 - {{ log.log_date|safe_datetime('%Y-%m-%d') }}</h1>
        <div class="nav-buttons">
            <a href="{{ url_for('daily_management.scan_upload_entry') }}" class="btn btn-info nav-btn" style="background: linear-gradient(135deg, #36b9cc, #1cc88a); position: relative; overflow: hidden;">
                <i class="fas fa-qrcode mr-1"></i> 扫码上传
                <span class="badge badge-light" style="position: absolute; top: -8px; right: -8px; border-radius: 50%; padding: 0.25rem; font-size: 0.6rem; box-shadow: 0 2px 5px rgba(0,0,0,0.2);">
                    <i class="fas fa-star"></i>
                </span>
            </a>

            {% if prev_log %}
            <a href="{{ url_for('daily_management.inspections', log_id=prev_log.id) }}" class="btn btn-primary nav-btn">
                <i class="fas fa-chevron-left mr-1"></i> 前一天
            </a>
            {% else %}
            <button class="btn btn-primary nav-btn disabled" disabled>
                <i class="fas fa-chevron-left mr-1"></i> 前一天
            </button>
            {% endif %}

            <div class="btn-group">
                <a href="{{ url_for('daily_management.edit_log', date_str=log.log_date|safe_datetime('%Y-%m-%d')) }}" class="btn btn-info nav-btn">
                    <i class="fas fa-calendar-day mr-1"></i> 日志详情
                </a>
                <button type="button" class="btn btn-info dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span class="sr-only">切换下拉菜单</span>
                </button>
                <div class="dropdown-menu dropdown-menu-right">
                    <form class="px-3 py-2" onsubmit="goToDate(event)">
                        <div class="form-group mb-2">
                            <label for="dateInput">选择日期</label>
                            <input type="date" class="form-control" id="dateInput" value="{{ log.log_date|safe_datetime('%Y-%m-%d') }}">
                        </div>
                        <button type="submit" class="btn btn-primary btn-sm btn-block">
                            <i class="fas fa-search mr-1"></i> 查看
                        </button>
                    </form>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" href="{{ url_for('daily_management.inspections_by_date', date_str=today|safe_datetime('%Y-%m-%d')) }}">
                        <i class="fas fa-calendar-day mr-1"></i> 今天
                    </a>
                    <a class="dropdown-item" href="{{ url_for('daily_management.inspections_by_date', date_str=(today - timedelta(days=7))|safe_datetime('%Y-%m-%d')) }}">
                        <i class="fas fa-calendar-week mr-1"></i> 上周同一天
                    </a>
                    <a class="dropdown-item" href="{{ url_for('daily_management.inspections_by_date', date_str=(today - timedelta(days=30))|safe_datetime('%Y-%m-%d')) }}">
                        <i class="fas fa-calendar-alt mr-1"></i> 上月同一天
                    </a>
                </div>
            </div>

            {% if next_log %}
            <a href="{{ url_for('daily_management.inspections', log_id=next_log.id) }}" class="btn btn-primary nav-btn">
                后一天 <i class="fas fa-chevron-right ml-1"></i>
            </a>
            {% else %}
            <button class="btn btn-primary nav-btn disabled" disabled>
                后一天 <i class="fas fa-chevron-right ml-1"></i>
            </button>
            {% endif %}
        </div>
    </div>

    <!-- 检查记录卡片 -->
    <div class="row">
        <!-- 晨检 -->
        <div class="col-12 mb-4">
            <div class="card shadow inspection-card">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-sun mr-1"></i> 晨检
                    </h6>
                    <div>
                        <a href="{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='morning') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit mr-1"></i> 编辑
                        </a>
                        <a href="{{ url_for('daily_management.generate_inspection_qrcode', log_id=log.id, inspection_type='morning') }}" class="btn btn-info btn-sm ml-2">
                            <i class="fas fa-qrcode mr-1"></i> 二维码
                        </a>
                        <a href="{{ url_for('daily_management.print_inspections', log_id=log.id) }}" class="btn btn-success btn-sm ml-2">
                            <i class="fas fa-print mr-1"></i> 打印
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if morning_inspections %}
                    <div class="table-responsive">
                        <table class="table table-bordered inspection-table" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th width="30%">检查项目</th>
                                    <th width="15%">状态</th>
                                    <th>详情</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for inspection in morning_inspections %}
                                <tr>
                                    <td class="font-weight-bold">{{ inspection.inspection_item }}</td>
                                    <td class="text-center">
                                        {% if inspection.status == 'normal' %}
                                        <span class="badge badge-success">正常</span>
                                        {% else %}
                                        <span class="badge badge-danger">异常</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if inspection.description %}
                                        <p class="mb-1">{{ inspection.description }}</p>
                                        {% else %}
                                        <p class="text-muted mb-1">无描述</p>
                                        {% endif %}

                                        {% if inspection.photos %}
                                        <div class="photo-gallery">
                                            {% for photo in inspection.photos %}
                                            <img src="{{ photo.file_path }}"
                                                 class="photo-thumbnail"
                                                 data-toggle="tooltip"
                                                 title="点击查看大图"
                                                 onclick="showFullImage('{{ photo.file_path }}', {{ photo.rating or 0 }})">
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-list fa-3x text-gray-300 mb-3"></i>
                        <p class="text-muted">暂无晨检记录</p>
                        <a href="{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='morning') }}" class="btn btn-light btn-sm mt-2">
                            <i class="fas fa-plus mr-1"></i> 添加记录
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 午检 -->
        <div class="col-12 mb-4">
            <div class="card shadow inspection-card">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-sun mr-1"></i> 午检
                    </h6>
                    <div>
                        <a href="{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='noon') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit mr-1"></i> 编辑
                        </a>
                        <a href="{{ url_for('daily_management.generate_inspection_qrcode', log_id=log.id, inspection_type='noon') }}" class="btn btn-info btn-sm ml-2">
                            <i class="fas fa-qrcode mr-1"></i> 二维码
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if noon_inspections %}
                    <div class="table-responsive">
                        <table class="table table-bordered inspection-table" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th width="30%">检查项目</th>
                                    <th width="15%">状态</th>
                                    <th>详情</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for inspection in noon_inspections %}
                                <tr>
                                    <td class="font-weight-bold">{{ inspection.inspection_item }}</td>
                                    <td class="text-center">
                                        {% if inspection.status == 'normal' %}
                                        <span class="badge badge-success">正常</span>
                                        {% else %}
                                        <span class="badge badge-danger">异常</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if inspection.description %}
                                        <p class="mb-1">{{ inspection.description }}</p>
                                        {% else %}
                                        <p class="text-muted mb-1">无描述</p>
                                        {% endif %}

                                        {% if inspection.photos %}
                                        <div class="photo-gallery">
                                            {% for photo in inspection.photos %}
                                            <img src="{{ photo.file_path }}"
                                                 class="photo-thumbnail"
                                                 data-toggle="tooltip"
                                                 title="点击查看大图"
                                                 onclick="showFullImage('{{ photo.file_path }}', {{ photo.rating or 0 }})">
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-list fa-3x text-gray-300 mb-3"></i>
                        <p class="text-muted">暂无午检记录</p>
                        <a href="{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='noon') }}" class="btn btn-light btn-sm mt-2">
                            <i class="fas fa-plus mr-1"></i> 添加记录
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 晚检 -->
        <div class="col-12 mb-4">
            <div class="card shadow inspection-card">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-moon mr-1"></i> 晚检
                    </h6>
                    <div>
                        <a href="{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='evening') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit mr-1"></i> 编辑
                        </a>
                        <a href="{{ url_for('daily_management.generate_inspection_qrcode', log_id=log.id, inspection_type='evening') }}" class="btn btn-info btn-sm ml-2">
                            <i class="fas fa-qrcode mr-1"></i> 二维码
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if evening_inspections %}
                    <div class="table-responsive">
                        <table class="table table-bordered inspection-table" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th width="30%">检查项目</th>
                                    <th width="15%">状态</th>
                                    <th>详情</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for inspection in evening_inspections %}
                                <tr>
                                    <td class="font-weight-bold">{{ inspection.inspection_item }}</td>
                                    <td class="text-center">
                                        {% if inspection.status == 'normal' %}
                                        <span class="badge badge-success">正常</span>
                                        {% else %}
                                        <span class="badge badge-danger">异常</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if inspection.description %}
                                        <p class="mb-1">{{ inspection.description }}</p>
                                        {% else %}
                                        <p class="text-muted mb-1">无描述</p>
                                        {% endif %}

                                        {% if inspection.photos %}
                                        <div class="photo-gallery">
                                            {% for photo in inspection.photos %}
                                            <img src="{{ photo.file_path }}"
                                                 class="photo-thumbnail"
                                                 data-toggle="tooltip"
                                                 title="点击查看大图"
                                                 onclick="showFullImage('{{ photo.file_path }}', {{ photo.rating or 0 }})">
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-list fa-3x text-gray-300 mb-3"></i>
                        <p class="text-muted">暂无晚检记录</p>
                        <a href="{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='evening') }}" class="btn btn-light btn-sm mt-2">
                            <i class="fas fa-plus mr-1"></i> 添加记录
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 二维码功能说明 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-qrcode mr-1"></i> 二维码功能
                    </h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="qrcodeDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="qrcodeDropdown">
                            <div class="dropdown-header">二维码操作:</div>
                            <a class="dropdown-item" href="{{ url_for('daily_management.generate_inspection_qrcode', log_id=log.id, inspection_type='morning') }}">
                                <i class="fas fa-qrcode fa-sm fa-fw mr-2 text-gray-400"></i> 晨检二维码
                            </a>
                            <a class="dropdown-item" href="{{ url_for('daily_management.generate_inspection_qrcode', log_id=log.id, inspection_type='noon') }}">
                                <i class="fas fa-qrcode fa-sm fa-fw mr-2 text-gray-400"></i> 午检二维码
                            </a>
                            <a class="dropdown-item" href="{{ url_for('daily_management.generate_inspection_qrcode', log_id=log.id, inspection_type='evening') }}">
                                <i class="fas fa-qrcode fa-sm fa-fw mr-2 text-gray-400"></i> 晚检二维码
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-camera text-primary mr-2"></i> 扫码上传照片
                                    </h5>
                                    <p class="card-text">员工可以通过扫描二维码上传检查照片，支持批量上传和添加描述。</p>
                                    <a href="{{ url_for('daily_management.generate_inspection_qrcode', log_id=log.id, inspection_type='morning') }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-qrcode mr-1"></i> 生成二维码
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-star text-warning mr-2"></i> 扫码评价照片
                                    </h5>
                                    <p class="card-text">管理员和其他用户可以通过扫描二维码对上传的照片进行星级评分。</p>
                                    <a href="{{ url_for('daily_management.generate_inspection_qrcode', log_id=log.id, inspection_type='morning') }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-qrcode mr-1"></i> 生成二维码
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-print text-success mr-2"></i> 打印二维码
                                    </h5>
                                    <p class="card-text">打印二维码并张贴在食堂，方便员工随时扫码上传照片和评价。</p>
                                    <a href="{{ url_for('daily_management.generate_inspection_qrcode', log_id=log.id, inspection_type='morning') }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-qrcode mr-1"></i> 生成二维码
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 照片管理区域 -->
    <div class="row">
        <!-- 晨检照片 -->
        <div class="col-lg-4 mb-4">
            {{ enhanced_image_uploader('inspection_morning', log.id, title='晨检照片管理', max_files=10, max_file_size=5, show_rating=True) }}
        </div>

        <!-- 午检照片 -->
        <div class="col-lg-4 mb-4">
            {{ enhanced_image_uploader('inspection_noon', log.id, title='午检照片管理', max_files=10, max_file_size=5, show_rating=True) }}
        </div>

        <!-- 晚检照片 -->
        <div class="col-lg-4 mb-4">
            {{ enhanced_image_uploader('inspection_evening', log.id, title='晚检照片管理', max_files=10, max_file_size=5, show_rating=True) }}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    // 初始化工具提示
    $(function () {
        $('[data-toggle="tooltip"]').tooltip();
    });

    // 显示大图
    function showFullImage(imageSrc, rating) {
        // 构建星级评分HTML
        let ratingHtml = '';
        for (let i = 1; i <= 5; i++) {
            if (i <= rating) {
                ratingHtml += '<i class="fas fa-star text-warning"></i>';
            } else {
                ratingHtml += '<i class="far fa-star text-warning"></i>';
            }
        }

        // 使用SweetAlert2显示大图
        Swal.fire({
            imageUrl: imageSrc,
            imageAlt: '检查照片',
            html: `
                <div class="mt-3">
                    <div class="rating mb-2">${ratingHtml}</div>
                </div>
            `,
            showCloseButton: true,
            showConfirmButton: false,
            width: 'auto',
            padding: '1rem',
            background: '#fff',
            backdrop: 'rgba(0,0,0,0.8)'
        });
    }

    // 跳转到指定日期
    function goToDate(event) {
        event.preventDefault();
        const dateInput = document.getElementById('dateInput');
        const selectedDate = dateInput.value;

        if (selectedDate) {
            window.location.href = "{{ url_for('daily_management.inspections_by_date', date_str='') }}" + selectedDate;
        }
    }

    // 键盘导航
    document.addEventListener('keydown', function(e) {
        // 左箭头键 - 前一天
        if (e.keyCode === 37) {
            {% if prev_log %}
            window.location.href = "{{ url_for('daily_management.inspections', log_id=prev_log.id) }}";
            {% endif %}
        }
        // 右箭头键 - 后一天
        else if (e.keyCode === 39) {
            {% if next_log %}
            window.location.href = "{{ url_for('daily_management.inspections', log_id=next_log.id) }}";
            {% endif %}
        }
        // D键 - 打开日期选择器
        else if (e.keyCode === 68 && !e.ctrlKey && !e.altKey && !e.metaKey) {
            const dateDropdown = document.querySelector('.dropdown-toggle-split');
            if (dateDropdown) {
                dateDropdown.click();
                setTimeout(() => {
                    const dateInput = document.getElementById('dateInput');
                    if (dateInput) {
                        dateInput.focus();
                    }
                }, 100);
            }
        }
    });
</script>
{% endblock %}
