<!DOCTYPE html>
<html>
<head>
    <title>出库单打印</title>
    <meta charset="utf-8">
    <style nonce="{{ csp_nonce }}">
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h2 {
            margin-bottom: 5px;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .info-table th, .info-table td {
            border: 1px solid #ddd;
            padding: 8px;
        }
        .info-table th {
            width: 25%;
            text-align: right;
            background-color: #f2f2f2;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
        }
        .items-table th, .items-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .items-table th {
            background-color: #f2f2f2;
        }
        .footer {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
        }
        .signature {
            width: 45%;
        }
        @media print {
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>出库单</h2>
        <p>单号：{{ stock_out.stock_out_number }}</p>
    </div>
    
    <table class="info-table">
        <tr>
            <th>仓库</th>
            <td>{{ stock_out.warehouse.name }}</td>
            <th>出库日期</th>
            <td>{{  stock_out.stock_out_date|format_datetime('%Y-%m-%d %H:%M:%S')  }}</td>
        </tr>
        <tr>
            <th>出库类型</th>
            <td>{{ stock_out.stock_out_type }}</td>
            <th>操作人</th>
            <td>{{ stock_out.operator.real_name or stock_out.operator.username }}</td>
        </tr>
        {% if consumption_plan and menu_plan %}
        <tr>
            <th>关联消耗计划</th>
            <td colspan="3">{{ consumption_plan.id }} ({{ menu_plan.plan_date }} {{ menu_plan.meal_type }})</td>
        </tr>
        {% endif %}
        <tr>
            <th>备注</th>
            <td colspan="3">{{ stock_out.notes or '-' }}</td>
        </tr>
    </table>
    
    <table class="items-table">
        <thead>
            <tr>
                <th>序号</th>
                <th>食材名称</th>
                <th>批次号</th>
                <th>出库数量</th>
                <th>单位</th>
            </tr>
        </thead>
        <tbody>
            {% for item in stock_out_items %}
            <tr>
                <td>{{ loop.index }}</td>
                <td>{{ item.ingredient.name }}</td>
                <td>{{ item.batch_number }}</td>
                <td>{{ item.quantity }}</td>
                <td>{{ item.unit }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    
    <div class="footer">
        <div class="signature">
            <p>出库人：{{ stock_out.operator.real_name or stock_out.operator.username }}</p>
            <p>签名：________________</p>
        </div>
        <div class="signature">
            <p>领用人：________________</p>
            <p>签名：________________</p>
        </div>
    </div>
    
    <div class="no-print" style="text-align: center; margin-top: 20px;">
        <button class="print-button">打印</button>
        <button data-data-data-data-data-data-onclick="window.close()" data-event-id="5585caa5" data-event-id="5585caa5" data-event-id="5585caa5" data-event-id="5585caa5" data-event-id="5585caa5" data-event-id="5585caa5">关闭</button>
    </div>
    
    <script nonce="{{ csp_nonce }}">
        // 页面加载完成后自动聚焦
        window.onload = function() {
            window.focus();
        };
    </script>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
</body>
</html>
