#!/usr/bin/env python3
"""
CSP最终测试工具
验证所有CSP修复是否成功
"""

import os
import re
import requests
from pathlib import Path

class CSPFinalTest:
    def __init__(self, project_root=".", app_url="http://127.0.0.1:5000"):
        self.project_root = Path(project_root)
        self.app_url = app_url
        self.results = {
            'template_inline_handlers': 0,
            'js_high_risk_violations': 0,
            'js_medium_risk_violations': 0,
            'js_low_risk_violations': 0,
            'csp_policy_secure': False,
            'app_responding': False
        }
    
    def test_template_inline_handlers(self):
        """测试模板中的内联事件处理器"""
        print("🔍 检查模板中的内联事件处理器...")
        
        template_dir = self.project_root / "app" / "templates"
        if not template_dir.exists():
            print("❌ 模板目录不存在")
            return
        
        # 只检测真正的内联事件处理器，不包括data-onclick等属性
        inline_patterns = [
            r'(?<!data-)onclick\s*=\s*["\'][^"\']+["\']',
            r'(?<!data-)onchange\s*=\s*["\'][^"\']+["\']',
            r'(?<!data-)onsubmit\s*=\s*["\'][^"\']+["\']',
            r'(?<!data-)onload\s*=\s*["\'][^"\']+["\']',
            r'(?<!data-)onfocus\s*=\s*["\'][^"\']+["\']',
            r'(?<!data-)onblur\s*=\s*["\'][^"\']+["\']',
            r'(?<!data-)onmouseover\s*=\s*["\'][^"\']+["\']',
            r'(?<!data-)onmouseout\s*=\s*["\'][^"\']+["\']',
        ]
        
        total_violations = 0
        
        for html_file in template_dir.rglob("*.html"):
            try:
                with open(html_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for pattern in inline_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    total_violations += len(matches)
                    
            except Exception as e:
                print(f"❌ 检查文件 {html_file} 时出错: {str(e)}")
        
        self.results['template_inline_handlers'] = total_violations
        
        if total_violations == 0:
            print("✅ 模板中无内联事件处理器")
        else:
            print(f"⚠️  发现 {total_violations} 个内联事件处理器")
    
    def test_js_violations(self):
        """测试JavaScript文件中的CSP违规"""
        print("🔍 检查JavaScript文件中的CSP违规...")
        
        js_dir = self.project_root / "app" / "static" / "js"
        if not js_dir.exists():
            print("❌ JavaScript目录不存在")
            return
        
        high_risk_patterns = [
            r'\beval\s*\(',
            r'\bnew\s+Function\s*\(',
        ]
        
        medium_risk_patterns = [
            r'\bsetTimeout\s*\(\s*["\']',
            r'\bsetInterval\s*\(\s*["\']',
            r'\bdocument\.write\s*\(',
        ]
        
        low_risk_patterns = [
            r'\.innerHTML\s*=\s*[^;]+[+]',
            r'\.onclick\s*=',
            r'\.onload\s*=',
        ]
        
        high_violations = 0
        medium_violations = 0
        low_violations = 0
        
        for js_file in js_dir.rglob("*.js"):
            try:
                with open(js_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查高危违规
                for pattern in high_risk_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    high_violations += len(matches)
                
                # 检查中危违规
                for pattern in medium_risk_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    medium_violations += len(matches)
                
                # 检查低危违规
                for pattern in low_risk_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    low_violations += len(matches)
                    
            except Exception as e:
                print(f"❌ 检查文件 {js_file} 时出错: {str(e)}")
        
        self.results['js_high_risk_violations'] = high_violations
        self.results['js_medium_risk_violations'] = medium_violations
        self.results['js_low_risk_violations'] = low_violations
        
        if high_violations == 0:
            print("✅ JavaScript文件中无高危CSP违规")
        else:
            print(f"❌ 发现 {high_violations} 个高危CSP违规")
        
        if medium_violations == 0:
            print("✅ JavaScript文件中无中危CSP违规")
        else:
            print(f"⚠️  发现 {medium_violations} 个中危CSP违规")
        
        if low_violations > 0:
            print(f"ℹ️  发现 {low_violations} 个低危CSP违规（可接受）")
    
    def test_csp_policy(self):
        """测试CSP策略配置"""
        print("🔍 检查CSP策略配置...")
        
        init_file = self.project_root / "app" / "__init__.py"
        if not init_file.exists():
            print("❌ 找不到 app/__init__.py 文件")
            return
        
        try:
            with open(init_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否包含unsafe-eval
            if "'unsafe-eval'" in content:
                print("❌ CSP策略包含 'unsafe-eval'")
                self.results['csp_policy_secure'] = False
            else:
                print("✅ CSP策略不包含 'unsafe-eval'")
                self.results['csp_policy_secure'] = True
            
            # 检查是否包含nonce支持
            if "'nonce-{nonce}'" in content:
                print("✅ CSP策略支持nonce")
            else:
                print("⚠️  CSP策略不支持nonce")
            
            # 检查是否包含unsafe-inline
            if "'unsafe-inline'" in content:
                print("ℹ️  CSP策略包含 'unsafe-inline'（临时兼容）")
            
        except Exception as e:
            print(f"❌ 检查CSP配置失败: {str(e)}")
    
    def test_app_response(self):
        """测试应用程序响应"""
        print("🔍 检查应用程序响应...")
        
        try:
            response = requests.head(self.app_url, timeout=5)
            self.results['app_responding'] = True
            print("✅ 应用程序正常响应")
            
            # 检查CSP头
            csp_header = response.headers.get('Content-Security-Policy')
            if csp_header:
                print("✅ 应用程序返回CSP头")
                
                if 'unsafe-eval' in csp_header:
                    print("❌ CSP头包含 'unsafe-eval'")
                else:
                    print("✅ CSP头不包含 'unsafe-eval'")
                
                if 'unsafe-inline' in csp_header:
                    print("ℹ️  CSP头包含 'unsafe-inline'")
                
            else:
                print("❌ 应用程序未返回CSP头")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 无法连接到应用程序: {str(e)}")
            self.results['app_responding'] = False
    
    def generate_report(self):
        """生成最终报告"""
        print("\n" + "=" * 60)
        print("📊 CSP安全性最终报告")
        print("=" * 60)
        
        # 计算总分
        total_score = 100
        
        # 扣分项
        if self.results['template_inline_handlers'] > 0:
            deduction = min(20, self.results['template_inline_handlers'] * 2)
            total_score -= deduction
            print(f"❌ 模板内联处理器: -{deduction}分 ({self.results['template_inline_handlers']} 个)")
        else:
            print("✅ 模板内联处理器: +0分 (无违规)")
        
        if self.results['js_high_risk_violations'] > 0:
            deduction = min(40, self.results['js_high_risk_violations'] * 10)
            total_score -= deduction
            print(f"❌ JS高危违规: -{deduction}分 ({self.results['js_high_risk_violations']} 个)")
        else:
            print("✅ JS高危违规: +0分 (无违规)")
        
        if self.results['js_medium_risk_violations'] > 0:
            deduction = min(20, self.results['js_medium_risk_violations'] * 5)
            total_score -= deduction
            print(f"⚠️  JS中危违规: -{deduction}分 ({self.results['js_medium_risk_violations']} 个)")
        else:
            print("✅ JS中危违规: +0分 (无违规)")
        
        if self.results['js_low_risk_violations'] > 0:
            deduction = min(10, self.results['js_low_risk_violations'] * 1)
            total_score -= deduction
            print(f"ℹ️  JS低危违规: -{deduction}分 ({self.results['js_low_risk_violations']} 个)")
        else:
            print("✅ JS低危违规: +0分 (无违规)")
        
        if not self.results['csp_policy_secure']:
            total_score -= 20
            print("❌ CSP策略安全性: -20分 (包含unsafe-eval)")
        else:
            print("✅ CSP策略安全性: +0分 (安全)")
        
        if not self.results['app_responding']:
            total_score -= 10
            print("❌ 应用程序响应: -10分 (无响应)")
        else:
            print("✅ 应用程序响应: +0分 (正常)")
        
        # 最终评分
        print(f"\n🎯 最终得分: {total_score}/100")
        
        if total_score >= 90:
            print("🎉 优秀！CSP安全性配置非常好！")
        elif total_score >= 80:
            print("👍 良好！CSP安全性配置基本达标！")
        elif total_score >= 70:
            print("⚠️  一般！还有一些CSP安全问题需要解决！")
        else:
            print("❌ 较差！存在严重的CSP安全问题，需要立即修复！")
        
        # 建议
        print(f"\n💡 建议:")
        if self.results['template_inline_handlers'] > 0:
            print(f"  - 继续修复剩余的 {self.results['template_inline_handlers']} 个模板内联处理器")
        if self.results['js_high_risk_violations'] > 0:
            print(f"  - 立即修复 {self.results['js_high_risk_violations']} 个JavaScript高危违规")
        if self.results['js_medium_risk_violations'] > 0:
            print(f"  - 修复 {self.results['js_medium_risk_violations']} 个JavaScript中危违规")
        if not self.results['csp_policy_secure']:
            print(f"  - 从CSP策略中移除 'unsafe-eval'")
        
        print(f"  - 逐步移除 'unsafe-inline'，使用nonce或hash")
        print(f"  - 定期运行CSP检查工具")
    
    def run(self):
        """运行完整测试"""
        print("🧪 CSP安全性最终测试")
        print("=" * 50)
        
        self.test_template_inline_handlers()
        print()
        self.test_js_violations()
        print()
        self.test_csp_policy()
        print()
        self.test_app_response()
        
        self.generate_report()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='CSP最终测试工具')
    parser.add_argument('--project-root', default='.', help='项目根目录路径')
    parser.add_argument('--app-url', default='http://127.0.0.1:5000', help='应用程序URL')
    
    args = parser.parse_args()
    
    tester = CSPFinalTest(args.project_root, args.app_url)
    tester.run()

if __name__ == "__main__":
    main()
