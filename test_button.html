<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮测试</title>
    <script src="/static/js/comprehensive-event-handler.js"></script>
</head>
<body>
    <h1>按钮测试</h1>
    
    <button type="button" class="btn btn-success" data-action="safe-navigate" data-navigate-code="window.location.href='/consumption-plan/super-editor'" style="cursor: pointer;">
        直接创建测试
    </button>
    
    <script>
        console.log('测试页面已加载');
        
        // 手动测试safeExecuteCode函数
        setTimeout(() => {
            console.log('测试safeExecuteCode函数:');
            if (typeof safeExecuteCode !== 'undefined') {
                console.log('safeExecuteCode函数存在');
                console.log('测试结果:', safeExecuteCode("window.location.href='/consumption-plan/super-editor'"));
            } else {
                console.log('safeExecuteCode函数不存在');
            }
        }, 1000);
    </script>
</body>
</html>
