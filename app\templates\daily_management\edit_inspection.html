{% extends 'base.html' %}

{% block title %}编辑检查记录
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .inspection-card {
        margin-bottom: 20px;
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
    }
    .inspection-card .card-header {
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
        padding: 0.75rem 1.25rem;
    }
    .inspection-card .card-body {
        padding: 1.25rem;
    }
    .photo-preview {
        max-width: 100%;
        max-height: 200px;
        margin-top: 10px;
    }
</style>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">
        编辑{{ '晨检' if inspection_type == 'morning' else '午检' if inspection_type == 'noon' else '晚检' }} - {{ log.log_date.strftime('%Y-%m-%d') }}
    </h1>

    <!-- 模板选择 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">检查模板</h6>
            <a href="{{ url_for('daily_management.inspection_templates', log_id=log.id, inspection_type=inspection_type) }}" class="btn btn-sm btn-primary">
                <i class="fas fa-cog mr-1"></i> 管理模板
            </a>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="templateSelect">选择检查模板</label>
                        <select class="form-control" id="templateSelect">
                            <option value="">-- 请选择模板 --</option>
                            <!-- 模板选项将通过JavaScript动态加载 -->
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>模板描述</label>
                        <p id="templateDescription" class="form-control-static">请选择一个模板查看描述</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">检查记录</h6>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>检查时间</label>
                            <input type="datetime-local" class="form-control" value="{{ now.strftime('%Y-%m-%dT%H:%M') if now else '' }}" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>检查人员</label>
                            <input type="text" class="form-control" value="{{ current_user.username }}" readonly>
                        </div>
                    </div>
                </div>

                <div class="row">
                    {% for item in inspection_items %}
                    <div class="col-md-6">
                        <div class="inspection-card">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold">{{ item }}</h6>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label>状态</label>
                                    <div class="btn-group btn-group-toggle w-100" data-toggle="buttons">
                                        <label class="btn btn-outline-success {{ 'active' if item in inspections and inspections[item].status == 'normal' else '' }}">
                                            <input type="radio" name="status_{{ item }}" value="normal" {{ 'checked' if item in inspections and inspections[item].status == 'normal' else '' }}> 正常
                                        </label>
                                        <label class="btn btn-outline-danger {{ 'active' if item in inspections and inspections[item].status == 'abnormal' else '' }}">
                                            <input type="radio" name="status_{{ item }}" value="abnormal" {{ 'checked' if item in inspections and inspections[item].status == 'abnormal' else '' }}> 异常
                                        </label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>备注</label>
                                    <textarea class="form-control" name="description_{{ item }}" rows="2">{{ inspections[item].description if item in inspections else '' }}</textarea>
                                </div>

                                <div class="form-group">
                                    <label>照片</label>
                                    <input type="file" class="form-control-file" name="photo_{{ item }}" accept="image/*" data-data-data-data-data-onchange="previewImage(this, " data-event-id="c1e4f600" data-event-id="c1e4f600" data-event-id="c1e4f600" data-event-id="c1e4f600" data-event-id="c1e4f600"preview_{{ item }}')">

                                    {% if item in inspections and inspections[item].photos %}
                                    <div class="mt-2">
                                        <p>已上传照片:</p>
                                        {% for photo in inspections[item].photos %}
                                        <img src="{{ photo.file_path }}" class="img-thumbnail" style="max-height: 100px; margin-right: 5px;">
                                        {% endfor %}
                                    </div>
                                    {% endif %}

                                    <div id="preview_{{ item }}" class="mt-2"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <div class="form-group text-center mt-4">
                    <button type="submit" class="btn btn-primary">保存检查记录</button>
                    <a href="{{ url_for('daily_management.inspections', log_id=log.id) }}" class="btn btn-secondary">返回检查记录</a>
                </div>
            </form>
        </div>
    </div>
</div>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    // 模板数据
    let templates = [];
    let currentTemplate = null;

    // 页面加载完成后执行
    $(document).ready(function() {
        // 获取模板数据
        fetchTemplates();

        // 初始化模板选择
        initTemplateSelect();

        // 检查URL参数中是否有模板ID
        const urlParams = new URLSearchParams(window.location.search);
        const templateId = urlParams.get('template_id');
        if (templateId) {
            // 从本地存储中获取选中的模板ID
            localStorage.removeItem('selectedTemplateId');

            // 设置选中的模板
            $('#templateSelect').val(templateId).trigger('change');
        } else {
            // 从本地存储中获取选中的模板ID
            const selectedTemplateId = localStorage.getItem('selectedTemplateId');
            if (selectedTemplateId) {
                $('#templateSelect').val(selectedTemplateId).trigger('change');
                localStorage.removeItem('selectedTemplateId');
            }
        }
    });

    // 获取模板数据
    function fetchTemplates() {
        // 根据检查类型确定模板分类
        let category = '';
        if ('{{ inspection_type }}' === 'morning' || '{{ inspection_type }}' === 'noon' || '{{ inspection_type }}' === 'evening') {
            category = '卫生检查'; // 默认使用卫生检查模板
        }

        // 发送API请求获取模板数据
        $.ajax({
            url: '/api/v2/inspection-templates',
            method: 'GET',
            data: { category: category },
            success: function(response) {
                templates = response;
                populateTemplateSelect(templates);
            },
            error: function(error) {
                console.error('获取模板数据失败:', error);
            }
        });
    }

    // 填充模板选择下拉框
    function populateTemplateSelect(templates) {
        const select = $('#templateSelect');

        // 清空选项
        select.find('option').not(':first').remove();

        // 添加模板选项
        templates.forEach(template => {
            select.append(`<option value="${template.id}">${template.name}</option>`);
        });
    }

    // 初始化模板选择
    function initTemplateSelect() {
        $('#templateSelect').change(function() {
            const templateId = $(this).val();

            if (!templateId) {
                // 未选择模板
                $('#templateDescription').text('请选择一个模板查看描述');
                currentTemplate = null;
                return;
            }

            // 查找选中的模板
            currentTemplate = templates.find(t => t.id == templateId);

            if (currentTemplate) {
                // 显示模板描述
                $('#templateDescription').text(currentTemplate.description || '无描述');

                // 应用模板
                applyTemplate(currentTemplate);
            }
        });
    }

    // 应用模板
    function applyTemplate(template) {
        if (!template || !template.items || template.items.length === 0) {
            return;
        }

        // 获取当前页面上的检查项目
        const currentItems = [];
        document.querySelectorAll('.inspection-card .card-header h6').forEach(function(header) {
            currentItems.push(header.textContent.trim());
        });

        // 检查是否有新的检查项目需要添加
        const newItems = template.items.filter(item => !currentItems.includes(item.name));

        if (newItems.length > 0) {
            // 提示用户是否应用模板
            if (confirm(`是否应用模板"${template.name}"？这将添加${newItems.length}个新的检查项目。`)) {
                // 重新加载页面，带上模板ID参数
                window.location.href = `${window.location.pathname}?template_id=${template.id}`;
            }
        } else {
            alert(`模板"${template.name}"中的检查项目已经存在于当前页面中。`);
        }
    }

    function previewImage(input, previewId) {
        var preview = document.getElementById(previewId);
        preview.innerHTML = '';

        if (input.files && input.files[0]) {
            var reader = new FileReader();

            reader.onload = function(e) {
                var img = document.createElement('img');
                img.src = e.target.result;
                img.className = 'photo-preview';
                preview.appendChild(img);
            }

            reader.readAsDataURL(input.files[0]);
        }
    }

    // 当选择"异常"时自动聚焦到备注输入框
    document.querySelectorAll('input[value="abnormal"]').forEach(function(radio) {
        radio.addEventListener('change', function() {
            if (this.checked) {
                var item = this.name.replace('status_', '');
                document.querySelector('textarea[name="description_' + item + '"]').focus();
            }
        });
    });
</script>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}
