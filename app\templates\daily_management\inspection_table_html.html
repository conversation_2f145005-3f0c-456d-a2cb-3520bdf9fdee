<!-- 检查记录表格 HTML 片段 -->
<div class="inspection-table-container">
    <h5 class="text-primary mb-3">{{ log.log_date|format_datetime('%Y-%m-%d') }} 检查记录</h5>

    <!-- 检查记录表格 -->
    <table class="inspection-table">
        <thead>
            <tr>
                <th width="60">时间</th>
                <th>地面卫生</th>
                <th>操作台卫生</th>
                <th>设备卫生</th>
                <th>食材存储</th>
                <th>人员卫生</th>
                <th>餐具消毒</th>
            </tr>
        </thead>
        <tbody>
            <!-- 晨检 -->
            <tr>
                <td class="time-cell">
                    <i class="fas fa-sun text-warning"></i><br>晨检
                </td>

                {% for item in ['地面卫生', '操作台卫生', '设备卫生', '食材存储', '人员卫生', '餐具消毒'] %}
                    {% set inspection = morning_inspections|selectattr('inspection_item', 'equalto', item)|first %}
                    <td class="inspection-cell">
                        {% if inspection %}
                            <div class="text-center">
                                {% if inspection.photos %}
                                    <div class="photo-gallery">
                                        {% for photo in inspection.photos %}
                                            <img src="{{ photo.file_path }}"
                                                 class="photo-thumbnail"
                                                 data-toggle="tooltip"
                                                 title="点击查看大图"
                                                 data-onclick="showFullImage(" data-event-id="f879f5d5"{{ photo.file_path }}', {{ photo.rating or 0 }})">
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    <div class="text-muted small">无照片</div>
                                {% endif %}
                            </div>

                            <div class="inspection-info">
                                <div class="label">检查情况：</div>
                                <div>{{ inspection.description or '-' }}</div>
                            </div>

                            <div class="inspection-info">
                                <div class="label">评分：</div>
                                <div class="rating">
                                    {% set rating = inspection.photos[0].rating if inspection.photos else 0 %}
                                    {% for i in range(1, 6) %}
                                        {% if i <= rating %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        {% else %}
                            <div class="text-center text-muted small">
                                暂无记录
                            </div>
                        {% endif %}
                    </td>
                {% endfor %}
            </tr>

            <!-- 午检 -->
            <tr>
                <td class="time-cell">
                    <i class="fas fa-utensils text-primary"></i><br>午检
                </td>

                {% for item in ['地面卫生', '操作台卫生', '设备卫生', '食材存储', '人员卫生', '餐具消毒'] %}
                    {% set inspection = noon_inspections|selectattr('inspection_item', 'equalto', item)|first %}
                    <td class="inspection-cell">
                        {% if inspection %}
                            <div class="text-center">
                                {% if inspection.photos %}
                                    <div class="photo-gallery">
                                        {% for photo in inspection.photos %}
                                            <img src="{{ photo.file_path }}"
                                                 class="photo-thumbnail"
                                                 data-toggle="tooltip"
                                                 title="点击查看大图"
                                                 data-onclick="showFullImage(" data-event-id="f879f5d5"{{ photo.file_path }}', {{ photo.rating or 0 }})">
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    <div class="text-muted small">无照片</div>
                                {% endif %}
                            </div>

                            <div class="inspection-info">
                                <div class="label">检查情况：</div>
                                <div>{{ inspection.description or '-' }}</div>
                            </div>

                            <div class="inspection-info">
                                <div class="label">评分：</div>
                                <div class="rating">
                                    {% set rating = inspection.photos[0].rating if inspection.photos else 0 %}
                                    {% for i in range(1, 6) %}
                                        {% if i <= rating %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        {% else %}
                            <div class="text-center text-muted small">
                                暂无记录
                            </div>
                        {% endif %}
                    </td>
                {% endfor %}
            </tr>

            <!-- 晚检 -->
            <tr>
                <td class="time-cell">
                    <i class="fas fa-moon text-info"></i><br>晚检
                </td>

                {% for item in ['地面卫生', '操作台卫生', '设备卫生', '食材存储', '人员卫生', '餐具消毒'] %}
                    {% set inspection = evening_inspections|selectattr('inspection_item', 'equalto', item)|first %}
                    <td class="inspection-cell">
                        {% if inspection %}
                            <div class="text-center">
                                {% if inspection.photos %}
                                    <div class="photo-gallery">
                                        {% for photo in inspection.photos %}
                                            <img src="{{ photo.file_path }}"
                                                 class="photo-thumbnail"
                                                 data-toggle="tooltip"
                                                 title="点击查看大图"
                                                 data-onclick="showFullImage(" data-event-id="f879f5d5"{{ photo.file_path }}', {{ photo.rating or 0 }})">
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    <div class="text-muted small">无照片</div>
                                {% endif %}
                            </div>

                            <div class="inspection-info">
                                <div class="label">检查情况：</div>
                                <div>{{ inspection.description or '-' }}</div>
                            </div>

                            <div class="inspection-info">
                                <div class="label">评分：</div>
                                <div class="rating">
                                    {% set rating = inspection.photos[0].rating if inspection.photos else 0 %}
                                    {% for i in range(1, 6) %}
                                        {% if i <= rating %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        {% else %}
                            <div class="text-center text-muted small">
                                暂无记录
                            </div>
                        {% endif %}
                    </td>
                {% endfor %}
            </tr>
        </tbody>
    </table>
</div>
