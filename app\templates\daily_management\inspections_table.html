{% extends 'base.html' %}

{% block title %}检查记录 - {{ log.log_date.strftime('%Y-%m-%d') }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    /* 日期导航样式 */
    .date-navigation {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
        background-color: #f8f9fc;
        border-radius: 0.35rem;
        padding: 0.5rem 1rem;
        box-shadow: 0 0.15rem 0.5rem rgba(58, 59, 69, 0.15);
    }

    .date-navigation .date-display {
        font-size: 1.25rem;
        font-weight: 700;
        color: #4e73df;
        margin: 0;
    }

    .date-navigation .nav-buttons {
        display: flex;
        gap: 0.5rem;
    }

    /* 检查表格样式 */
    .inspection-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 1rem;
        border: 1px solid #e3e6f0;
    }

    .inspection-table th {
        background-color: #f8f9fc;
        color: #4e73df;
        font-weight: 600;
        text-align: center;
        padding: 0.75rem;
        border: 1px solid #e3e6f0;
    }

    .inspection-table td {
        padding: 0.75rem;
        border: 1px solid #e3e6f0;
        vertical-align: top;
    }

    .inspection-table .time-cell {
        background-color: #f8f9fc;
        font-weight: 600;
        text-align: center;
        width: 80px;
    }

    /* 照片展示样式 */
    .photo-gallery {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-top: 0.5rem;
    }

    .photo-thumbnail {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 0.25rem;
        border: 1px solid #e3e6f0;
        cursor: pointer;
        transition: all 0.2s;
    }

    .photo-thumbnail:hover {
        transform: scale(1.05);
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    /* 评分星星样式 */
    .rating {
        color: #f6c23e;
        font-size: 0.8rem;
        margin-top: 0.5rem;
    }

    /* 检查项目单元格样式 */
    .inspection-cell {
        background-color: #f9f3e8;
        min-height: 150px;
    }

    /* 检查情况样式 */
    .inspection-info {
        margin-top: 0.5rem;
    }

    .inspection-info .label {
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .date-navigation {
            flex-direction: column;
            gap: 0.5rem;
        }

        .inspection-table {
            font-size: 0.9rem;
        }

        .inspection-table th,
        .inspection-table td {
            padding: 0.5rem;
        }
    }
</style>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 日期导航 -->
    <div class="date-navigation">
        <h1 class="date-display">检查记录 - {{ log.log_date.strftime('%Y-%m-%d') }}</h1>
        <div class="nav-buttons">
            {% if prev_log %}
            <a href="{{ url_for('daily_management.inspections_table', log_id=prev_log.id) }}" class="btn btn-primary btn-sm">
                <i class="fas fa-chevron-left mr-1"></i> 前一天
            </a>
            {% else %}
            <button class="btn btn-primary btn-sm disabled" disabled>
                <i class="fas fa-chevron-left mr-1"></i> 前一天
            </button>
            {% endif %}

            <div class="btn-group">
                <a href="{{ url_for('daily_management.edit_log', date_str=log.log_date.strftime('%Y-%m-%d')) }}" class="btn btn-info btn-sm">
                    <i class="fas fa-calendar-day mr-1"></i> 日志详情
                </a>
                <button type="button" class="btn btn-info btn-sm dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span class="sr-only">切换下拉菜单</span>
                </button>
                <div class="dropdown-menu dropdown-menu-right">
                    <form class="px-3 py-2" data-data-data-data-onsubmit="goToDate(event)" data-event-id="1f21e4b2" data-event-id="1f21e4b2" data-event-id="1f21e4b2" data-event-id="1f21e4b2">
                        <div class="form-group mb-2">
                            <label for="dateInput">选择日期</label>
                            <input type="date" class="form-control" id="dateInput" value="{{ log.log_date.strftime('%Y-%m-%d') }}">
                        </div>
                        <button type="submit" class="btn btn-primary btn-sm btn-block">
                            <i class="fas fa-search mr-1"></i> 查看
                        </button>
                    </form>
                </div>
            </div>

            {% if next_log %}
            <a href="{{ url_for('daily_management.inspections_table', log_id=next_log.id) }}" class="btn btn-primary btn-sm">
                后一天 <i class="fas fa-chevron-right ml-1"></i>
            </a>
            {% else %}
            <button class="btn btn-primary btn-sm disabled" disabled>
                后一天 <i class="fas fa-chevron-right ml-1"></i>
            </button>
            {% endif %}
        </div>
    </div>

    <!-- 检查记录表格 -->
    <table class="inspection-table">
        <thead>
            <tr>
                <th width="80">时间</th>
                <th>地面卫生</th>
                <th>操作台卫生</th>
                <th>设备卫生</th>
                <th>食材存储</th>
                <th>人员卫生</th>
                <th>餐具消毒</th>
            </tr>
        </thead>
        <tbody>
            <!-- 晨检 -->
            <tr>
                <td class="time-cell">
                    <i class="fas fa-sun text-warning"></i><br>晨检
                </td>

                {% for item in ['地面卫生', '操作台卫生', '设备卫生', '食材存储', '人员卫生', '餐具消毒'] %}
                    {% set inspection = morning_inspections|selectattr('inspection_item', 'equalto', item)|first %}
                    <td class="inspection-cell">
                        {% if inspection %}
                            <div class="text-center">
                                {% if inspection.photos %}
                                    <div class="photo-gallery justify-content-center">
                                        {% for photo in inspection.photos %}
                                            <img src="{{ photo.file_path }}"
                                                 class="photo-thumbnail"
                                                 data-toggle="tooltip"
                                                 title="点击查看大图"
                                                 data-data-data-data-onclick="showFullImage(" data-event-id="f879f5d5" data-event-id="f879f5d5" data-event-id="f879f5d5" data-event-id="f879f5d5"{{ photo.file_path }}', {{ photo.rating or 0 }})">
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    <div class="text-muted">可分页显示照片</div>
                                {% endif %}
                            </div>

                            <div class="inspection-info">
                                <div class="label">检查情况：</div>
                                <div>{{ inspection.description or '-' }}</div>
                            </div>

                            <div class="inspection-info">
                                <div class="label">评分：</div>
                                <div class="rating">
                                    {% set rating = inspection.photos[0].rating if inspection.photos else 0 %}
                                    {% for i in range(1, 6) %}
                                        {% if i <= rating %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        {% else %}
                            <div class="text-center text-muted">
                                可分页显示照片
                            </div>
                        {% endif %}
                    </td>
                {% endfor %}
            </tr>

            <!-- 午检 -->
            <tr>
                <td class="time-cell">
                    <i class="fas fa-utensils text-primary"></i><br>午检
                </td>

                {% for item in ['地面卫生', '操作台卫生', '设备卫生', '食材存储', '人员卫生', '餐具消毒'] %}
                    {% set inspection = noon_inspections|selectattr('inspection_item', 'equalto', item)|first %}
                    <td class="inspection-cell">
                        {% if inspection %}
                            <div class="text-center">
                                {% if inspection.photos %}
                                    <div class="photo-gallery justify-content-center">
                                        {% for photo in inspection.photos %}
                                            <img src="{{ photo.file_path }}"
                                                 class="photo-thumbnail"
                                                 data-toggle="tooltip"
                                                 title="点击查看大图"
                                                 data-data-data-data-onclick="showFullImage(" data-event-id="f879f5d5" data-event-id="f879f5d5" data-event-id="f879f5d5" data-event-id="f879f5d5"{{ photo.file_path }}', {{ photo.rating or 0 }})">
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    <div class="text-muted">可分页显示照片</div>
                                {% endif %}
                            </div>

                            <div class="inspection-info">
                                <div class="label">检查情况：</div>
                                <div>{{ inspection.description or '-' }}</div>
                            </div>

                            <div class="inspection-info">
                                <div class="label">评分：</div>
                                <div class="rating">
                                    {% set rating = inspection.photos[0].rating if inspection.photos else 0 %}
                                    {% for i in range(1, 6) %}
                                        {% if i <= rating %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        {% else %}
                            <div class="text-center text-muted">
                                可分页显示照片
                            </div>
                        {% endif %}
                    </td>
                {% endfor %}
            </tr>

            <!-- 晚检 -->
            <tr>
                <td class="time-cell">
                    <i class="fas fa-moon text-info"></i><br>晚检
                </td>

                {% for item in ['地面卫生', '操作台卫生', '设备卫生', '食材存储', '人员卫生', '餐具消毒'] %}
                    {% set inspection = evening_inspections|selectattr('inspection_item', 'equalto', item)|first %}
                    <td class="inspection-cell">
                        {% if inspection %}
                            <div class="text-center">
                                {% if inspection.photos %}
                                    <div class="photo-gallery justify-content-center">
                                        {% for photo in inspection.photos %}
                                            <img src="{{ photo.file_path }}"
                                                 class="photo-thumbnail"
                                                 data-toggle="tooltip"
                                                 title="点击查看大图"
                                                 data-data-data-data-onclick="showFullImage(" data-event-id="f879f5d5" data-event-id="f879f5d5" data-event-id="f879f5d5" data-event-id="f879f5d5"{{ photo.file_path }}', {{ photo.rating or 0 }})">
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    <div class="text-muted">可分页显示照片</div>
                                {% endif %}
                            </div>

                            <div class="inspection-info">
                                <div class="label">检查情况：</div>
                                <div>{{ inspection.description or '-' }}</div>
                            </div>

                            <div class="inspection-info">
                                <div class="label">评分：</div>
                                <div class="rating">
                                    {% set rating = inspection.photos[0].rating if inspection.photos else 0 %}
                                    {% for i in range(1, 6) %}
                                        {% if i <= rating %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        {% else %}
                            <div class="text-center text-muted">
                                可分页显示照片
                            </div>
                        {% endif %}
                    </td>
                {% endfor %}
            </tr>
        </tbody>
    </table>

    <!-- 底部导航 -->
    <div class="d-flex justify-content-between mb-4">
        <div>
            <a href="{{ url_for('daily_management.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-home mr-1"></i> 返回首页
            </a>
            <a href="{{ url_for('daily_management.logs') }}" class="btn btn-info btn-sm ml-2">
                <i class="fas fa-list mr-1"></i> 所有日志
            </a>
        </div>
        <div>
            <a href="{{ url_for('daily_management.inspections', log_id=log.id) }}" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-th-large mr-1"></i> 原卡片视图
            </a>
            <a href="{{ url_for('daily_management.inspections_category_cards', log_id=log.id) }}" class="btn btn-outline-primary btn-sm ml-2">
                <i class="fas fa-th-large mr-1"></i> 类别卡片
            </a>
            <a href="{{ url_for('daily_management.inspections_card_layout', log_id=log.id) }}" class="btn btn-outline-primary btn-sm ml-2">
                <i class="fas fa-th mr-1"></i> 卡片布局
            </a>
            <a href="{{ url_for('daily_management.inspections_simple_table', log_id=log.id) }}" class="btn btn-outline-primary btn-sm ml-2">
                <i class="fas fa-border-all mr-1"></i> 简单表格
            </a>
        </div>
    </div>
</div>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    // 初始化工具提示
    $(function () {
        $('[data-toggle="tooltip"]').tooltip();
    });

    // 显示大图
    function showFullImage(imageSrc, rating) {
        // 构建星级评分HTML
        let ratingHtml = '';
        for (let i = 1; i <= 5; i++) {
            if (i <= rating) {
                ratingHtml += '<i class="fas fa-star text-warning"></i>';
            } else {
                ratingHtml += '<i class="far fa-star text-warning"></i>';
            }
        }

        // 使用SweetAlert2显示大图
        Swal.fire({
            imageUrl: imageSrc,
            imageAlt: '检查照片',
            html: `
                <div class="mt-3">
                    <div class="rating mb-2">${ratingHtml}</div>
                </div>
            `,
            showCloseButton: true,
            showConfirmButton: false,
            width: 'auto',
            padding: '1rem',
            background: '#fff',
            backdrop: 'rgba(0,0,0,0.8)'
        });
    }

    // 跳转到指定日期
    function goToDate(event) {
        event.preventDefault();
        const dateInput = document.getElementById('dateInput');
        const selectedDate = dateInput.value;

        if (selectedDate) {
            window.location.href = "{{ url_for('daily_management.inspections_by_date', date_str='') }}" + selectedDate + "?view=table";
        }
    }

    // 键盘导航
    document.addEventListener('keydown', function(e) {
        // 左箭头键 - 前一天
        if (e.keyCode === 37) {
            const prevBtn = document.querySelector('.nav-buttons a:first-child');
            if (prevBtn && !prevBtn.classList.contains('disabled')) {
                prevBtn.click();
            }
        }
        // 右箭头键 - 后一天
        else if (e.keyCode === 39) {
            const nextBtn = document.querySelector('.nav-buttons a:last-child');
            if (nextBtn && !nextBtn.classList.contains('disabled')) {
                nextBtn.click();
            }
        }
    });
</script>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}
