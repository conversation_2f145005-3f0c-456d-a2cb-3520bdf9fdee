/**
 * 用户引导系统 JavaScript
 * 管理分步骤的用户引导流程
 */

class UserGuide {
    constructor() {
        this.currentStep = 'welcome';
        this.totalSteps = 11;
        this.stepOrder = [
            'welcome', 'daily_management', 'suppliers', 'ingredients_recipes',
            'weekly_menu', 'purchase_order', 'stock_in', 'consumption_plan',
            'stock_out', 'traceability', 'food_samples', 'completed'
        ];
        this.schoolType = null;
        this.videoPlayer = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadGuideStatus();
    }

    bindEvents() {
        // 下一步按钮
        $('#nextStepBtn').on('click', () => {
            this.nextStep();
        });

        // 上一步按钮
        $('#prevStepBtn').on('click', () => {
            this.prevStep();
        });

        // 跳过引导按钮
        $('#skipGuideBtn').on('click', () => {
            this.skipGuide();
        });

        // 模态框关闭事件
        $('#guideStepModal').on('hidden.bs.modal', () => {
            this.pauseGuide();
        });
    }

    loadGuideStatus() {
        $.ajax({
            url: '/api/guide/status',
            type: 'GET',
            success: (data) => {
                if (data.is_active && data.current_step !== 'completed') {
                    this.currentStep = data.current_step;
                    this.schoolType = data.school_type;
                    this.showStep(this.currentStep);
                }
            },
            error: (xhr, status, error) => {
                console.error('加载引导状态失败:', error);
            }
        });
    }

    initVideoPlayer() {
        // 初始化视频播放器
        if (!this.videoPlayer) {
            this.videoPlayer = {
                currentVideo: null,
                isPlaying: false,

                play: function(videoUrl, title) {
                    this.currentVideo = {
                        url: videoUrl,
                        title: title
                    };
                    this.showVideoModal(videoUrl, title);
                },

                showVideoModal: function(videoUrl, title) {
                    const modalHtml = `
                        <div class="modal fade" id="videoModal" tabindex="-1" role="dialog">
                            <div class="modal-dialog modal-lg" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">
                                            <i class="fas fa-play-circle mr-2"></i>${title}
                                        </h5>
                                        <button type="button" class="close" data-dismiss="modal">
                                            <span>&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body p-0">
                                        <video controls width="100%" height="400">
                                            <source src="${videoUrl}" type="video/mp4">
                                            您的浏览器不支持视频播放。
                                        </video>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">
                                            关闭
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // 移除已存在的视频模态框
                    $('#videoModal').remove();

                    // 添加新的视频模态框
                    $('body').append(modalHtml);
                    $('#videoModal').modal('show');

                    // 模态框关闭时停止视频
                    $('#videoModal').on('hidden.bs.modal', function() {
                        $(this).remove();
                    });
                }
            };
        }
    }

    showStep(stepName) {
        this.currentStep = stepName;

        // 更新进度条
        const stepIndex = this.stepOrder.indexOf(stepName);
        const progress = (stepIndex / (this.totalSteps - 1)) * 100;
        $('#guideProgress').css('width', progress + '%');

        // 加载步骤内容
        this.loadStepContent(stepName);

        // 显示模态框
        $('#guideStepModal').modal('show');

        // 更新按钮状态
        this.updateButtons(stepIndex);
    }

    loadStepContent(stepName) {
        const templateId = stepName + 'StepTemplate';
        const template = $('#' + templateId).html();

        if (template) {
            $('#stepContent').html(template);

            // 更新标题
            const stepInfo = this.getStepInfo(stepName);
            $('#stepTitle').text(stepInfo.title);
        } else {
            // 如果没有模板，通过AJAX加载
            $.ajax({
                url: `/api/guide/step/${stepName}`,
                type: 'GET',
                success: (data) => {
                    $('#stepContent').html(data.content);
                    $('#stepTitle').text(data.title);
                },
                error: (xhr, status, error) => {
                    console.error('加载步骤内容失败:', error);
                    $('#stepContent').html('<p class="text-danger">加载内容失败，请刷新页面重试。</p>');
                }
            });
        }
    }

    updateButtons(stepIndex) {
        // 上一步按钮
        if (stepIndex > 0) {
            $('#prevStepBtn').show();
        } else {
            $('#prevStepBtn').hide();
        }

        // 下一步按钮文本
        if (stepIndex === this.totalSteps - 1) {
            $('#nextStepBtn').html('<i class="fas fa-check ml-1"></i>完成引导');
        } else {
            $('#nextStepBtn').html('<i class="fas fa-arrow-right ml-1"></i>下一步');
        }
    }

    nextStep() {
        const currentIndex = this.stepOrder.indexOf(this.currentStep);

        if (currentIndex < this.totalSteps - 1) {
            const nextStep = this.stepOrder[currentIndex + 1];

            // 标记当前步骤为完成
            this.completeStep(this.currentStep, () => {
                this.showStep(nextStep);
            });
        } else {
            // 完成引导
            this.completeGuide();
        }
    }

    prevStep() {
        const currentIndex = this.stepOrder.indexOf(this.currentStep);

        if (currentIndex > 0) {
            const prevStep = this.stepOrder[currentIndex - 1];
            this.showStep(prevStep);
        }
    }

    completeStep(stepName, callback) {
        $.ajax({
            url: '/api/guide/complete-step',
            type: 'POST',
            data: {
                step: stepName
            },
            success: (data) => {
                if (callback) callback();
            },
            error: (xhr, status, error) => {
                console.error('完成步骤失败:', error);
                if (callback) callback(); // 即使失败也继续
            }
        });
    }

    completeGuide() {
        $.ajax({
            url: '/api/guide/complete',
            type: 'POST',
            success: (data) => {
                $('#guideStepModal').modal('hide');
                this.showCompletionMessage();
            },
            error: (xhr, status, error) => {
                console.error('完成引导失败:', error);
                $('#guideStepModal').modal('hide');
            }
        });
    }

    skipGuide() {
        if (confirm('确定要跳过引导吗？您可以随时在帮助菜单中重新开始。')) {
            $.ajax({
                url: '/api/guide/skip',
                type: 'POST',
                success: (data) => {
                    $('#guideStepModal').modal('hide');
                },
                error: (xhr, status, error) => {
                    console.error('跳过引导失败:', error);
                    $('#guideStepModal').modal('hide');
                }
            });
        }
    }

    pauseGuide() {
        // 暂停引导，保存当前状态
        console.log('引导已暂停，当前步骤:', this.currentStep);
    }

    showCompletionMessage() {
        const message = `
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <h5><i class="fas fa-graduation-cap mr-2"></i>恭喜您完成了系统引导！</h5>
                <p>您已经掌握了食堂管理的完整流程，现在可以开始正式使用系统了。</p>
                <hr>
                <p class="mb-0">
                    <a href="${window.location.origin}/help" class="btn btn-sm btn-outline-success mr-2">
                        <i class="fas fa-question-circle mr-1"></i>查看帮助文档
                    </a>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="userGuide.restartGuide()">
                        <i class="fas fa-redo mr-1"></i>重新开始引导
                    </button>
                </p>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;

        // 在页面顶部显示完成消息
        $('body').prepend(message);

        // 滚动到顶部
        $('html, body').animate({ scrollTop: 0 }, 500);
    }

    restartGuide() {
        $.ajax({
            url: '/api/guide/restart',
            type: 'POST',
            success: (data) => {
                this.currentStep = 'welcome';
                this.showStep('welcome');
            },
            error: (xhr, status, error) => {
                console.error('重启引导失败:', error);
            }
        });
    }

    getStepInfo(stepName) {
        const stepInfoMap = {
            'welcome': { title: '欢迎使用校园餐智慧食堂平台' },
            'daily_management': { title: '食堂日常管理模块' },
            'suppliers': { title: '供应商管理' },
            'ingredients_recipes': { title: '食材食谱管理' },
            'weekly_menu': { title: '周菜单计划' },
            'purchase_order': { title: '采购订单' },
            'stock_in': { title: '食材入库' },
            'consumption_plan': { title: '消耗量计划' },
            'stock_out': { title: '食材出库' },
            'traceability': { title: '食材溯源' },
            'food_samples': { title: '留样记录' },
            'completed': { title: '引导完成' }
        };

        return stepInfoMap[stepName] || { title: '系统引导' };
    }
}

// 全局函数，供模板调用
function generateQRCode() {
    // 生成检查二维码的逻辑
    alert('正在生成学校专属检查二维码...');
    // 这里可以调用实际的二维码生成API
}

function playVideo(stepName, videoName = null) {
    // 播放指定步骤的视频
    if (typeof userGuide !== 'undefined') {
        userGuide.initVideoPlayer();

        // 获取视频资源
        $.ajax({
            url: `/api/guide/videos/${stepName}`,
            type: 'GET',
            success: (data) => {
                if (data.success && data.videos && data.videos.length > 0) {
                    let video = data.videos[0]; // 默认播放第一个视频

                    // 如果指定了视频名称，查找对应视频
                    if (videoName) {
                        const foundVideo = data.videos.find(v => v.name === videoName);
                        if (foundVideo) {
                            video = foundVideo;
                        }
                    }

                    userGuide.videoPlayer.play(video.url, video.name);
                } else {
                    alert('暂无可用的演示视频');
                }
            },
            error: (xhr, status, error) => {
                console.error('获取视频资源失败:', error);
                alert('视频加载失败，请稍后重试');
            }
        });
    }
}

function createDemoSupplier() {
    $.ajax({
        url: '/api/guide/create-demo/suppliers',
        type: 'POST',
        success: (data) => {
            if (data.success) {
                alert('演示供应商创建成功！您可以在供应商管理页面查看。');
            } else {
                alert('创建失败：' + data.message);
            }
        },
        error: (xhr, status, error) => {
            console.error('创建演示供应商失败:', error);
            alert('创建失败，请稍后重试。');
        }
    });
}

function createDemoIngredients() {
    $.ajax({
        url: '/api/guide/create-demo/ingredients_recipes',
        type: 'POST',
        success: (data) => {
            if (data.success) {
                alert('演示食材和食谱创建成功！您可以在相应页面查看。');
            } else {
                alert('创建失败：' + data.message);
            }
        },
        error: (xhr, status, error) => {
            console.error('创建演示食材失败:', error);
            alert('创建失败，请稍后重试。');
        }
    });
}

function createDemoStockIn() {
    $.ajax({
        url: '/api/guide/create-demo/stock_in',
        type: 'POST',
        success: (data) => {
            if (data.success) {
                alert('演示入库记录创建成功！您可以在入库管理页面查看。');
            } else {
                alert('创建失败：' + data.message);
            }
        },
        error: (xhr, status, error) => {
            console.error('创建演示入库失败:', error);
            alert('创建失败，请稍后重试。');
        }
    });
}

function demoTraceability() {
    // 演示溯源查询功能
    alert('正在演示食材溯源查询功能...\n\n1. 输入食材批次号\n2. 查看完整溯源链条\n3. 分析各环节信息');

    // 可以打开溯源页面进行演示
    window.open('/traceability', '_blank');
}

function generateSampleRecord() {
    $.ajax({
        url: '/api/guide/create-demo/food_sample',
        type: 'POST',
        success: (data) => {
            if (data.success) {
                alert('演示留样记录生成成功！已自动生成今日留样记录。');
                if (data.pdf_url) {
                    // 如果有PDF链接，可以打开查看
                    window.open(data.pdf_url, '_blank');
                }
            } else {
                alert('生成失败：' + data.message);
            }
        },
        error: (xhr, status, error) => {
            console.error('生成留样记录失败:', error);
            alert('生成失败，请稍后重试。');
        }
    });
}

// 初始化用户引导
let userGuide;
$(document).ready(function() {
    userGuide = new UserGuide();
});
