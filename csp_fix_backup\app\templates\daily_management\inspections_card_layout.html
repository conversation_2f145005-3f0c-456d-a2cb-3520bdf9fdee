{% extends 'base.html' %}

{% block title %}检查记录 - {{ log.log_date|format_datetime('%Y-%m-%d') }}{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    /* 日期导航样式 */
    .date-navigation {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
        background-color: #f8f9fc;
        border-radius: 0.35rem;
        padding: 0.5rem 1rem;
        box-shadow: 0 0.15rem 0.5rem rgba(58, 59, 69, 0.15);
    }

    .date-navigation .date-display {
        font-size: 1.25rem;
        font-weight: 700;
        color: #4e73df;
        margin: 0;
    }

    .date-navigation .nav-buttons {
        display: flex;
        gap: 0.5rem;
    }

    /* 检查记录卡片样式 */
    .inspection-cards {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .inspection-card {
        flex: 1;
        min-width: 300px;
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        overflow: hidden;
    }

    .inspection-card-header {
        background-color: #f8f9fc;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #e3e6f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .inspection-card-header h5 {
        margin: 0;
        font-weight: 600;
        color: #4e73df;
    }

    .inspection-card-body {
        padding: 0;
    }

    /* 检查项目表格样式 */
    .inspection-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 0;
    }

    .inspection-table th,
    .inspection-table td {
        padding: 0.75rem;
        border: 1px solid #e3e6f0;
    }

    .inspection-table th {
        background-color: #f8f9fc;
        font-weight: 600;
        text-align: center;
    }

    .inspection-table td {
        vertical-align: middle;
    }

    .inspection-table .item-cell {
        width: 100px;
        background-color: #f8f9fc;
        font-weight: 600;
        text-align: center;
    }

    /* 状态标签样式 */
    .status-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 600;
        text-align: center;
        min-width: 40px;
    }

    .status-badge.status-normal {
        background-color: #1cc88a;
        color: white;
    }

    /* 照片展示样式 */
    .photo-gallery {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-top: 0.5rem;
    }

    .photo-thumbnail {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 0.25rem;
        border: 1px solid #e3e6f0;
        cursor: pointer;
        transition: all 0.2s;
    }

    .photo-thumbnail:hover {
        transform: scale(1.05);
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .date-navigation {
            flex-direction: column;
            gap: 0.5rem;
        }

        .inspection-cards {
            flex-direction: column;
        }

        .inspection-card {
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 日期导航 -->
    <div class="date-navigation">
        <h1 class="date-display">检查记录 - {{ log.log_date|format_datetime('%Y-%m-%d') }}</h1>
        <div class="nav-buttons">
            {% if prev_log %}
            <a href="{{ url_for('daily_management.inspections_card_layout', log_id=prev_log.id) }}" class="btn btn-primary btn-sm">
                <i class="fas fa-chevron-left mr-1"></i> 前一天
            </a>
            {% else %}
            <button class="btn btn-primary btn-sm disabled" disabled>
                <i class="fas fa-chevron-left mr-1"></i> 前一天
            </button>
            {% endif %}

            <div class="btn-group">
                <a href="{{ url_for('daily_management.edit_log', date_str=log.log_date|format_datetime('%Y-%m-%d')) }}" class="btn btn-info btn-sm">
                    <i class="fas fa-calendar-day mr-1"></i> 日志详情
                </a>
                <button type="button" class="btn btn-info btn-sm dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span class="sr-only">切换下拉菜单</span>
                </button>
                <div class="dropdown-menu dropdown-menu-right">
                    <form class="px-3 py-2" onsubmit="goToDate(event)">
                        <div class="form-group mb-2">
                            <label for="dateInput">选择日期</label>
                            <input type="date" class="form-control" id="dateInput" value="{{ log.log_date|format_datetime('%Y-%m-%d') }}">
                        </div>
                        <button type="submit" class="btn btn-primary btn-sm btn-block">
                            <i class="fas fa-search mr-1"></i> 查看
                        </button>
                    </form>
                </div>
            </div>

            {% if next_log %}
            <a href="{{ url_for('daily_management.inspections_card_layout', log_id=next_log.id) }}" class="btn btn-primary btn-sm">
                后一天 <i class="fas fa-chevron-right ml-1"></i>
            </a>
            {% else %}
            <button class="btn btn-primary btn-sm disabled" disabled>
                后一天 <i class="fas fa-chevron-right ml-1"></i>
            </button>
            {% endif %}
        </div>
    </div>

    <!-- 检查记录卡片 -->
    <div class="inspection-cards">
        <!-- 晨检卡片 -->
        <div class="inspection-card">
            <div class="inspection-card-header">
                <h5><i class="fas fa-sun text-warning mr-2"></i> 晨检</h5>
                <a href="{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='morning') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-edit"></i> 编辑
                </a>
            </div>
            <div class="inspection-card-body">
                <table class="inspection-table">
                    <thead>
                        <tr>
                            <th>检查项目</th>
                            <th>状态</th>
                            <th>详情</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in ['地面卫生', '操作台卫生', '设备卫生', '食材存储', '人员卫生', '餐具消毒'] %}
                            {% set inspection = morning_inspections|selectattr('inspection_item', 'equalto', item)|first %}
                            <tr>
                                <td class="item-cell">{{ item }}</td>
                                <td class="text-center">
                                    {% if inspection %}
                                        <span class="status-badge status-normal">正常</span>
                                    {% else %}
                                        <span class="text-muted">未检查</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if inspection %}
                                        {% if inspection.description %}
                                            <div>{{ inspection.description }}</div>
                                        {% else %}
                                            <div class="text-muted">无描述</div>
                                        {% endif %}

                                        {% if inspection.photos %}
                                            <div class="photo-gallery">
                                                {% for photo in inspection.photos %}
                                                    <img src="{{ photo.file_path }}"
                                                         class="photo-thumbnail"
                                                         data-toggle="tooltip"
                                                         title="点击查看大图"
                                                         onclick="showFullImage('{{ photo.file_path }}', {{ photo.rating or 0 }})">
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    {% else %}
                                        <div class="text-muted">无详情</div>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 午检卡片 -->
        <div class="inspection-card">
            <div class="inspection-card-header">
                <h5><i class="fas fa-utensils text-primary mr-2"></i> 午检</h5>
                <a href="{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='noon') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-edit"></i> 编辑
                </a>
            </div>
            <div class="inspection-card-body">
                <table class="inspection-table">
                    <thead>
                        <tr>
                            <th>检查项目</th>
                            <th>状态</th>
                            <th>详情</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in ['地面卫生', '操作台卫生', '设备卫生', '食材存储', '人员卫生', '餐具消毒'] %}
                            {% set inspection = noon_inspections|selectattr('inspection_item', 'equalto', item)|first %}
                            <tr>
                                <td class="item-cell">{{ item }}</td>
                                <td class="text-center">
                                    {% if inspection %}
                                        <span class="status-badge status-normal">正常</span>
                                    {% else %}
                                        <span class="text-muted">未检查</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if inspection %}
                                        {% if inspection.description %}
                                            <div>{{ inspection.description }}</div>
                                        {% else %}
                                            <div class="text-muted">无描述</div>
                                        {% endif %}

                                        {% if inspection.photos %}
                                            <div class="photo-gallery">
                                                {% for photo in inspection.photos %}
                                                    <img src="{{ photo.file_path }}"
                                                         class="photo-thumbnail"
                                                         data-toggle="tooltip"
                                                         title="点击查看大图"
                                                         onclick="showFullImage('{{ photo.file_path }}', {{ photo.rating or 0 }})">
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    {% else %}
                                        <div class="text-muted">无详情</div>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 晚检卡片 -->
        <div class="inspection-card">
            <div class="inspection-card-header">
                <h5><i class="fas fa-moon text-info mr-2"></i> 晚检</h5>
                <a href="{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='evening') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-edit"></i> 编辑
                </a>
            </div>
            <div class="inspection-card-body">
                <table class="inspection-table">
                    <thead>
                        <tr>
                            <th>检查项目</th>
                            <th>状态</th>
                            <th>详情</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in ['地面卫生', '操作台卫生', '设备卫生', '食材存储', '人员卫生', '餐具消毒'] %}
                            {% set inspection = evening_inspections|selectattr('inspection_item', 'equalto', item)|first %}
                            <tr>
                                <td class="item-cell">{{ item }}</td>
                                <td class="text-center">
                                    {% if inspection %}
                                        <span class="status-badge status-normal">正常</span>
                                    {% else %}
                                        <span class="text-muted">未检查</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if inspection %}
                                        {% if inspection.description %}
                                            <div>{{ inspection.description }}</div>
                                        {% else %}
                                            <div class="text-muted">无描述</div>
                                        {% endif %}

                                        {% if inspection.photos %}
                                            <div class="photo-gallery">
                                                {% for photo in inspection.photos %}
                                                    <img src="{{ photo.file_path }}"
                                                         class="photo-thumbnail"
                                                         data-toggle="tooltip"
                                                         title="点击查看大图"
                                                         onclick="showFullImage('{{ photo.file_path }}', {{ photo.rating or 0 }})">
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    {% else %}
                                        <div class="text-muted">无详情</div>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="d-flex justify-content-between mb-4">
        <div>
            <a href="{{ url_for('daily_management.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-home mr-1"></i> 返回首页
            </a>
            <a href="{{ url_for('daily_management.logs') }}" class="btn btn-info btn-sm ml-2">
                <i class="fas fa-list mr-1"></i> 所有日志
            </a>
        </div>
        <div>
            <a href="{{ url_for('daily_management.inspections', log_id=log.id) }}" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-th-large mr-1"></i> 原卡片视图
            </a>
            <a href="{{ url_for('daily_management.inspections_table', log_id=log.id) }}" class="btn btn-outline-primary btn-sm ml-2">
                <i class="fas fa-table mr-1"></i> 表格视图
            </a>
            <a href="{{ url_for('daily_management.inspections_simple_table', log_id=log.id) }}" class="btn btn-outline-primary btn-sm ml-2">
                <i class="fas fa-border-all mr-1"></i> 简单表格
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    // 初始化工具提示
    $(function () {
        $('[data-toggle="tooltip"]').tooltip();
    });

    // 显示大图
    function showFullImage(imageSrc, rating) {
        // 构建星级评分HTML
        let ratingHtml = '';
        for (let i = 1; i <= 5; i++) {
            if (i <= rating) {
                ratingHtml += '<i class="fas fa-star text-warning"></i>';
            } else {
                ratingHtml += '<i class="far fa-star text-warning"></i>';
            }
        }

        // 使用SweetAlert2显示大图
        Swal.fire({
            imageUrl: imageSrc,
            imageAlt: '检查照片',
            html: `
                <div class="mt-3">
                    <div class="rating mb-2">${ratingHtml}</div>
                </div>
            `,
            showCloseButton: true,
            showConfirmButton: false,
            width: 'auto',
            padding: '1rem',
            background: '#fff',
            backdrop: 'rgba(0,0,0,0.8)'
        });
    }

    // 跳转到指定日期
    function goToDate(event) {
        event.preventDefault();
        const dateInput = document.getElementById('dateInput');
        const selectedDate = dateInput.value;

        if (selectedDate) {
            window.location.href = "{{ url_for('daily_management.inspections_by_date', date_str='') }}" + selectedDate + "?view=card_layout";
        }
    }

    // 键盘导航
    document.addEventListener('keydown', function(e) {
        // 左箭头键 - 前一天
        if (e.keyCode === 37) {
            const prevBtn = document.querySelector('.nav-buttons a:first-child');
            if (prevBtn && !prevBtn.classList.contains('disabled')) {
                prevBtn.click();
            }
        }
        // 右箭头键 - 后一天
        else if (e.keyCode === 39) {
            const nextBtn = document.querySelector('.nav-buttons a:last-child');
            if (nextBtn && !nextBtn.classList.contains('disabled')) {
                nextBtn.click();
            }
        }
    });
</script>
{% endblock %}
