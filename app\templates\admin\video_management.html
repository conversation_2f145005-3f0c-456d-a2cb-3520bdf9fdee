{% extends "base.html" %}

{% block title %}视频资源管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-video mr-2"></i>用户引导视频资源管理
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#uploadVideoModal">
                            <i class="fas fa-upload mr-1"></i>上传视频
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for step_name, step_videos in video_resources.items() %}
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">{{ step_videos.title }}</h6>
                                    <small>总时长: {{ step_videos.duration }}</small>
                                </div>
                                <div class="card-body">
                                    {% if step_videos.videos %}
                                        {% for video in step_videos.videos %}
                                        <div class="video-item mb-3 p-2 border rounded">
                                            <div class="row">
                                                <div class="col-4">
                                                    <img src="{{ video.thumbnail }}" class="img-fluid rounded" alt="视频缩略图"
                                                         onerror="this.onerror=null; this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2Y4ZjlmYSIvPjx0ZXh0IHg9IjUwIiB5PSI1MCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjEyIiBmaWxsPSIjNmM3NTdkIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+6KeG6aKRPC90ZXh0Pjwvc3ZnPg=='">
                                                </div>
                                                <div class="col-8">
                                                    <h6 class="mb-1">{{ video.name }}</h6>
                                                    <p class="small text-muted mb-1">{{ video.duration }}</p>
                                                    <p class="small mb-2">{{ video.description }}</p>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-primary" data-onclick="previewVideo("{{ video.url }}', '{{ video.name }}')">
                                                            <i class="fas fa-play"></i>
                                                        </button>
                                                        <button class="btn btn-outline-warning" data-onclick="editVideo("{{ step_name }}', '{{ video.name }}')">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-outline-danger" data-action="critical-confirm" data-original-data-action="safe-delete" data-delete-code="deleteVideo(" style="cursor: pointer;">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    {% else %}
                                        <div class="text-center text-muted">
                                            <i class="fas fa-video-slash fa-2x mb-2"></i>
                                            <p>暂无视频资源</p>
                                            <button class="btn btn-sm btn-outline-primary" data-onclick="uploadVideoForStep("{{ step_name }}')" data-confirm-message="确定要删除吗？" style="cursor: pointer;">
                                                <i class="fas fa-plus mr-1"></i>添加视频
                                            </button>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 上传视频模态框 -->
<div class="modal fade" id="uploadVideoModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-upload mr-2"></i>上传引导视频
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="uploadVideoForm" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="stepSelect">引导步骤</label>
                        <select class="form-control" id="stepSelect" name="step_name" required>
                            <option value="">请选择引导步骤</option>
                            <option value="daily_management">日常管理模块</option>
                            <option value="suppliers">供应商管理</option>
                            <option value="ingredients_recipes">食材食谱管理</option>
                            <option value="weekly_menu">周菜单制定</option>
                            <option value="purchase_order">采购订单管理</option>
                            <option value="stock_in">食材入库管理</option>
                            <option value="consumption_plan">消耗量计划</option>
                            <option value="stock_out">食材出库管理</option>
                            <option value="traceability">食材溯源管理</option>
                            <option value="food_samples">留样记录管理</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="videoName">视频名称</label>
                        <input type="text" class="form-control" id="videoName" name="video_name" required>
                    </div>

                    <div class="form-group">
                        <label for="videoDescription">视频描述</label>
                        <textarea class="form-control" id="videoDescription" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="videoFile">视频文件</label>
                        <input type="file" class="form-control-file" id="videoFile" name="video_file" accept="video/*" required>
                        <small class="form-text text-muted">支持MP4、AVI、MOV格式，建议文件大小不超过100MB</small>
                    </div>

                    <div class="form-group">
                        <label for="thumbnailFile">缩略图</label>
                        <input type="file" class="form-control-file" id="thumbnailFile" name="thumbnail_file" accept="image/*">
                        <small class="form-text text-muted">可选，支持JPG、PNG格式</small>
                    </div>
                </form>

                <div id="uploadProgress" class="progress mt-3" style="display: none;">
                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" data-onclick="uploadVideo()">
                    <i class="fas fa-upload mr-1"></i>上传视频
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 视频预览模态框 -->
<div class="modal fade" id="videoPreviewModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewVideoTitle">视频预览</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body p-0">
                <video id="previewVideo" controls width="100%" height="400" style="display: none;">
                    您的浏览器不支持视频播放。
                </video>
                <div id="videoLoadError" class="text-center p-4" style="display: none;">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h5>视频加载失败</h5>
                    <p class="text-muted">视频文件可能不存在或格式不支持</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
function previewVideo(videoUrl, videoName) {
    $('#previewVideoTitle').text(videoName);
    const video = $('#previewVideo')[0];

    video.src = videoUrl;
    video.style.display = 'block';
    $('#videoLoadError').hide();

    video.onerror = function() {
        video.style.display = 'none';
        $('#videoLoadError').show();
    };

    $('#videoPreviewModal').modal('show');
}

function uploadVideoForStep(stepName) {
    $('#stepSelect').val(stepName);
    $('#uploadVideoModal').modal('show');
}

function uploadVideo() {
    const form = $('#uploadVideoForm')[0];
    const formData = new FormData(form);

    // 显示进度条
    $('#uploadProgress').show();

    $.ajax({
        url: '/admin/videos/upload',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            xhr.upload.addEventListener("progress", function(evt) {
                if (evt.lengthComputable) {
                    const percentComplete = evt.loaded / evt.total * 100;
                    $('.progress-bar').css('width', percentComplete + '%');
                }
            }, false);
            return xhr;
        },
        success: function(data) {
            if (data.success) {
                alert('视频上传成功！');
                $('#uploadVideoModal').modal('hide');
                location.reload();
            } else {
                alert('上传失败：' + data.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('上传失败:', error);
            alert('上传失败，请稍后重试');
        },
        complete: function() {
            $('#uploadProgress').hide();
            $('.progress-bar').css('width', '0%');
        }
    });
}

function editVideo(stepName, videoName) {
    // 编辑视频信息
    alert('编辑功能开发中...');
}

function deleteVideo(stepName, videoName) {
    if (confirm('确定要删除这个视频吗？此操作不可恢复。')) {
        $.ajax({
            url: '/admin/videos/delete',
            type: 'POST',
            data: {
                step_name: stepName,
                video_name: videoName
            },
            success: function(data) {
                if (data.success) {
                    alert('视频删除成功！');
                    location.reload();
                } else {
                    alert('删除失败：' + data.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('删除失败:', error);
                alert('删除失败，请稍后重试');
            }
        });
    }
}
</script>
{% endblock %}

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>