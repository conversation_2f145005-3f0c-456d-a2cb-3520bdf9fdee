{% extends 'base.html' %}

{% block title %}库存汇总
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">📦 库存汇总</h5>
                    <div class="d-flex align-items-center">
                        <div class="btn-group mr-2">
                            <a href="{{ url_for('inventory.index', view_type='detail') }}" class="btn btn-sm {% if view_type == 'detail' %}btn-primary text-white{% else %}btn-outline-secondary{% endif %}">
                                <i class="fas fa-list"></i> 详细视图
                            </a>
                            <a href="{{ url_for('inventory.index', view_type='summary') }}" class="btn btn-sm {% if view_type == 'summary' %}btn-primary text-white{% else %}btn-outline-secondary{% endif %}">
                                <i class="fas fa-chart-pie"></i> 汇总视图
                            </a>
                        </div>
                        <a href="{{ url_for('inventory.check_expiry') }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-exclamation-triangle"></i> 检查临期/过期
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 搜索表单 -->
                    <form method="get" action="{{ url_for('inventory.index') }}" class="mb-4">
                        <input type="hidden" name="view_type" value="{{ view_type }}">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>仓库</label>
                                    <select name="warehouse_id" class="form-control" id="warehouse_id" data-onchange="loadStorageLocations()">
                                        <option value="">全部</option>
                                        {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.id }}" {% if warehouse_id == warehouse.id %}selected{% endif %}>{{ warehouse.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>存储位置</label>
                                    <select name="storage_location_id" class="form-control" id="storage_location_id">
                                        <option value="">全部</option>
                                        {% for location in storage_locations %}
                                        <option value="{{ location.id }}" {% if storage_location_id == location.id %}selected{% endif %}>{{ location.name }} ({{ location.location_code }})</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>食材</label>
                                    <select name="ingredient_id" class="form-control">
                                        <option value="">全部</option>
                                        {% for ingredient in ingredients %}
                                        <option value="{{ ingredient.id }}" {% if ingredient_id == ingredient.id %}selected{% endif %}>{{ ingredient.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary btn-block">搜索</button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 库存汇总 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>食材类别</th>
                                    <th>食材名称</th>
                                    <th>总库存</th>
                                    <th>单位</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in inventory_summary %}
                                <tr>
                                    <td>{{ item.ingredient_category }}</td>
                                    <td>{{ item.ingredient_name }}</td>
                                    <td>{{ item.total_quantity }}</td>
                                    <td>{{ item.unit }}</td>
                                    <td>
                                        <a href="{{ url_for('inventory.ingredient_inventory', id=item.inventories_ingredient_id) }}" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i> 查看详情
                                        </a>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="5" class="text-center">暂无库存数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    // 加载存储位置
    function loadStorageLocations() {
        const warehouseId = document.getElementById('warehouse_id').value;
        const storageLocationSelect = document.getElementById('storage_location_id');

        // 清空现有选项
        storageLocationSelect.innerHTML = '<option value="">全部</option>';

        if (!warehouseId) return;

        // 发送AJAX请求获取存储位置
        fetch(`{{ url_for('inventory.get_storage_locations') }}?warehouse_id=${warehouseId}`)
            .then(response => response.json())
            .then(data => {
                data.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.id;
                    option.textContent = location.name;
                    storageLocationSelect.appendChild(option);
                });
            })
            .catch(error => console.error('Error loading storage locations:', error));
    }
</script>

{% endblock %}
