#!/usr/bin/env python3
"""
强制恢复工具
将所有data-onclick等属性恢复为原始的onclick属性
"""

import os
import re
from pathlib import Path

def force_restore_onclick():
    """强制恢复所有onclick属性"""
    
    print("🔄 强制恢复onclick属性")
    print("=" * 40)
    
    template_dir = Path("app/templates")
    if not template_dir.exists():
        print("❌ 模板目录不存在")
        return
    
    fixed_files = []
    
    for html_file in template_dir.rglob("*.html"):
        try:
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 恢复所有data-onclick为onclick
            # 匹配: data-data-data-onclick="..." 或 data-onclick="..."
            content = re.sub(r'data-(?:data-)*onclick="([^"]*)"(?:\s+data-event-id="[^"]*")*', r'onclick="\1"', content)
            
            # 恢复其他事件类型
            content = re.sub(r'data-(?:data-)*onchange="([^"]*)"(?:\s+data-event-id="[^"]*")*', r'onchange="\1"', content)
            content = re.sub(r'data-(?:data-)*onsubmit="([^"]*)"(?:\s+data-event-id="[^"]*")*', r'onsubmit="\1"', content)
            content = re.sub(r'data-(?:data-)*onload="([^"]*)"(?:\s+data-event-id="[^"]*")*', r'onload="\1"', content)
            content = re.sub(r'data-(?:data-)*onfocus="([^"]*)"(?:\s+data-event-id="[^"]*")*', r'onfocus="\1"', content)
            content = re.sub(r'data-(?:data-)*onblur="([^"]*)"(?:\s+data-event-id="[^"]*")*', r'onblur="\1"', content)
            content = re.sub(r'data-(?:data-)*onmouseover="([^"]*)"(?:\s+data-event-id="[^"]*")*', r'onmouseover="\1"', content)
            content = re.sub(r'data-(?:data-)*onmouseout="([^"]*)"(?:\s+data-event-id="[^"]*")*', r'onmouseout="\1"', content)
            
            # 移除重复的universal-event-handler.js引用
            lines = content.split('\n')
            seen_universal_handler = False
            filtered_lines = []
            
            for line in lines:
                if 'universal-event-handler.js' in line:
                    if not seen_universal_handler:
                        seen_universal_handler = True
                        filtered_lines.append(line)
                    # 跳过重复的引用
                else:
                    filtered_lines.append(line)
            
            content = '\n'.join(filtered_lines)
            
            # 如果内容有变化，写回文件
            if content != original_content:
                with open(html_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                rel_path = html_file.relative_to(Path("."))
                fixed_files.append(rel_path)
                print(f"✅ 恢复文件: {rel_path}")
                
        except Exception as e:
            print(f"❌ 处理文件 {html_file} 时出错: {str(e)}")
    
    print(f"\n🎉 恢复完成!")
    print(f"✅ 恢复了 {len(fixed_files)} 个文件")
    
    if fixed_files:
        print("\n恢复的文件:")
        for file_path in fixed_files:
            print(f"  - {file_path}")

def main():
    """主函数"""
    force_restore_onclick()

if __name__ == "__main__":
    main()
