{% extends "base.html" %}

{% block title %}{% if carousel %}编辑轮播图{% else %}添加轮播图{% endif %}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-white py-3">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-{% if carousel %}edit{% else %}plus{% endif %} me-2"></i>
                        {% if carousel %}编辑轮播图{% else %}添加轮播图{% endif %}
                    </h3>
                </div>

                <form method="POST" enctype="multipart/form-data" id="carouselForm" class="needs-validation" novalidate>
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <!-- 标题 -->
                                <div class="mb-4">
                                    <label for="title" class="form-label">
                                        图片标题 <span class="text-danger">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control form-control-lg"
                                           id="title"
                                           name="title"
                                           value="{{ carousel.title if carousel else '' }}"
                                           maxlength="100"
                                           required>
                                    <div class="form-text">建议长度：10-50个字符</div>
                                </div>

                                <!-- 描述 -->
                                <div class="mb-4">
                                    <label for="description" class="form-label">图片描述</label>
                                    <textarea class="form-control"
                                              id="description"
                                              name="description"
                                              rows="3"
                                              maxlength="500">{{ carousel.description if carousel else '' }}</textarea>
                                    <div class="form-text">可选，用于SEO和无障碍访问</div>
                                </div>

                                <!-- 链接地址 -->
                                <div class="mb-4">
                                    <label for="link_url" class="form-label">点击链接</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-link"></i></span>
                                        <input type="text"
                                               class="form-control"
                                               id="link_url"
                                               name="link_url"
                                               value="{{ carousel.link_url if carousel else '/' }}"
                                               placeholder="/">
                                    </div>
                                    <div class="form-text">
                                        点击图片时跳转的链接<br>
                                        <small class="text-muted">默认跳转到首页 (/)，可修改为其他链接</small>
                                    </div>
                                </div>

                                <!-- 状态 -->
                                <div class="mb-4">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input"
                                               type="checkbox"
                                               id="is_active"
                                               name="is_active"
                                               value="1"
                                               {% if not carousel or carousel.is_active %}checked{% endif %}>
                                        <label class="form-check-label" for="is_active">启用轮播图</label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- 图片上传 -->
                                <div class="mb-4">
                                    <label class="form-label">
                                        {% if carousel %}更换图片{% else %}上传图片{% endif %}
                                        <span class="text-danger">*</span>
                                    </label>
                                    <div class="card">
                                        <div class="card-body p-3">
                                            <div class="text-center mb-3">
                                                {% if carousel and carousel.image_path %}
                                                <img src="{{ carousel.image_path }}"
                                                     alt="{{ carousel.title }}"
                                                     class="img-fluid rounded mb-3"
                                                     style="max-height: 200px;">
                                                {% endif %}
                                                <div class="dropzone" id="imageDropzone">
                                                    <input type="file"
                                                           class="form-control"
                                                           id="images"
                                                           name="images"
                                                           accept="image/*"
                                                           {% if not carousel %}required{% endif %}>
                                                    <div class="dropzone-text">
                                                        <i class="fas fa-cloud-upload-alt fa-2x mb-2"></i>
                                                        <p class="mb-0">拖放图片到此处或点击上传</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-text">
                                                支持 jpg、png、gif 格式<br>
                                                建议尺寸：1920x600 像素
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer bg-white py-3">
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('homepage_carousel.admin_list') }}" class="btn btn-light">
                                <i class="fas fa-arrow-left me-1"></i>返回列表
                            </a>
                            <div>
                                <button type="button" class="btn btn-outline-secondary me-2" data-data-data-data-onclick="resetForm()" data-event-id="60bcf14c" data-event-id="60bcf14c" data-event-id="60bcf14c" data-event-id="60bcf14c">
                                    <i class="fas fa-undo me-1"></i>重置
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>
                                    {% if carousel %}更新轮播图{% else %}创建轮播图{% endif %}
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
// 表单验证
(function() {
    'use strict';
    
    const form = document.getElementById('carouselForm');
    const titleInput = document.getElementById('title');
    const imageInput = document.getElementById('images');
    
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        
        // 显示加载状态
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>处理中...';
        submitBtn.disabled = true;
        
        // 如果验证失败，恢复按钮状态
        setTimeout(() => {
            if (submitBtn.disabled) {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        }, 10000);
        
        form.classList.add('was-validated');
    });
    
    // 图片预览
    imageInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.createElement('img');
                preview.src = e.target.result;
                preview.className = 'img-fluid rounded mb-3';
                preview.style.maxHeight = '200px';
                
                const dropzone = document.getElementById('imageDropzone');
                const existingPreview = dropzone.querySelector('img');
                if (existingPreview) {
                    dropzone.removeChild(existingPreview);
                }
                dropzone.insertBefore(preview, dropzone.firstChild);
            };
            reader.readAsDataURL(file);
        }
    });
    
    // 重置表单
    window.resetForm = function() {
        if (confirm('确定要重置表单吗？所有未保存的更改将丢失。')) {
            form.reset();
            form.classList.remove('was-validated');
            
            // 恢复默认值
            {% if not carousel %}
            document.getElementById('link_url').value = '/';
            document.getElementById('is_active').checked = true;
            {% endif %}
            
            // 清除图片预览
            const dropzone = document.getElementById('imageDropzone');
            const preview = dropzone.querySelector('img');
            if (preview) {
                dropzone.removeChild(preview);
            }
        }
    };
})();
</script>

<style>
.dropzone {
    border: 2px dashed #dee2e6;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.dropzone:hover {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
}

.dropzone input[type="file"] {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0;
    cursor: pointer;
}

.dropzone-text {
    color: #6c757d;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}
</style>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}