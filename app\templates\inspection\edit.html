{% extends 'base.html' %}

{% block title %}编辑入库检查
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .card {
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        transition: all 0.3s;
        margin-bottom: 1.5rem;
    }
    
    .card-header {
        background: linear-gradient(to right, #f8f9fc, #ffffff);
        border-bottom: 1px solid #e3e6f0;
        border-radius: 8px 8px 0 0;
        padding: 1rem 1.25rem;
    }
    
    .card-body {
        padding: 1.25rem;
    }
    
    .form-group label {
        font-weight: 600;
        color: #4e73df;
    }
    
    .status-badge {
        padding: 0.4em 0.8em;
        font-weight: 500;
        border-radius: 20px;
    }
    
    .status-normal {
        background-color: #e6f7ee;
        color: #1f9d55;
        border: 1px solid #c3e6cb;
    }
    
    .status-abnormal {
        background-color: #fde8e8;
        color: #e53e3e;
        border: 1px solid #f5c6cb;
    }
    
    .status-pending {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeeba;
    }
    
    .photo-gallery {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 0.75rem;
        margin-top: 0.75rem;
    }
    
    .photo-thumbnail {
        width: 120px;
        height: 120px;
        object-fit: cover;
        border-radius: 0.375rem;
        border: 1px solid #e3e6f0;
        cursor: pointer;
        transition: all 0.2s;
    }
    
    .photo-thumbnail:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .table th {
        background: linear-gradient(to right, #f8f9fc, #ffffff);
        font-weight: 600;
        padding: 0.75rem 1rem;
        border-bottom: 2px solid #e3e6f0;
    }
    
    .table td {
        vertical-align: middle;
        padding: 1rem;
    }
    
    .btn {
        display: inline-flex;
        align-items: center;
        gap: 0.375rem;
        font-weight: 500;
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
        transition: all 0.2s;
    }
    
    .btn:hover {
        transform: translateY(-1px);
    }
    
    .btn i {
        font-size: 0.875rem;
    }
    
    .rating {
        display: flex;
        flex-direction: row-reverse;
        justify-content: flex-end;
    }
    
    .rating > input {
        display: none;
    }
    
    .rating > label {
        position: relative;
        width: 1.1em;
        font-size: 2em;
        color: #FFD700;
        cursor: pointer;
    }
    
    .rating > label::before {
        content: "\\2605";
        position: absolute;
        opacity: 0;
    }
    
    .rating > label:hover:before,
    .rating > label:hover ~ label:before {
        opacity: 1 !important;
    }
    
    .rating > input:checked ~ label:before {
        opacity: 1;
    }
    
    .rating:hover > input:checked ~ label:before {
        opacity: 0.4;
    }
</style>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">编辑入库检查</h1>
        <div>
            <a href="{{ url_for('inspection.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-1"></i> 返回列表
            </a>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-clipboard-check mr-1"></i> 检查信息
                    </h6>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="inspection_item">检查项目</label>
                                    <input type="text" class="form-control" id="inspection_item" name="inspection_item" value="{{ inspection.inspection_item }}" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status">检查状态</label>
                                    <select class="form-control" id="status" name="status" required>
                                        <option value="pending" {% if inspection.status == 'pending' %}selected{% endif %}>待检查</option>
                                        <option value="normal" {% if inspection.status == 'normal' %}selected{% endif %}>正常</option>
                                        <option value="abnormal" {% if inspection.status == 'abnormal' %}selected{% endif %}>异常</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="description">检查描述</label>
                            <textarea class="form-control" id="description" name="description" rows="3">{{ inspection.description }}</textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="photos">上传照片</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="photos" name="photos" multiple accept="image/*">
                                <label class="custom-file-label" for="photos">选择文件...</label>
                            </div>
                            <small class="form-text text-muted">可以选择多张照片，每张照片大小不超过5MB</small>
                        </div>
                        
                        <div class="form-group">
                            <label>照片评分</label>
                            <div class="rating">
                                <input type="radio" id="star5" name="rating" value="5" /><label for="star5" title="5分"></label>
                                <input type="radio" id="star4" name="rating" value="4" /><label for="star4" title="4分"></label>
                                <input type="radio" id="star3" name="rating" value="3" checked /><label for="star3" title="3分"></label>
                                <input type="radio" id="star2" name="rating" value="2" /><label for="star2" title="2分"></label>
                                <input type="radio" id="star1" name="rating" value="1" /><label for="star1" title="1分"></label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-1"></i> 保存检查记录
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    {% if purchase_order %}
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-shopping-cart mr-1"></i> 采购订单信息
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <tr>
                                <th width="20%">订单编号</th>
                                <td>{{ purchase_order.id }}</td>
                                <th width="20%">供应商</th>
                                <td>{{ purchase_order.supplier.name if purchase_order.supplier else '未知' }}</td>
                            </tr>
                            <tr>
                                <th>订单状态</th>
                                <td>{{ purchase_order.status }}</td>
                                <th>创建时间</th>
                                <td>{{ purchase_order.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                            <tr>
                                <th>备注</th>
                                <td colspan="3">{{ purchase_order.notes or '无' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    {% if photos %}
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-images mr-1"></i> 已上传照片
                    </h6>
                </div>
                <div class="card-body">
                    <div class="photo-gallery">
                        {% for photo in photos %}
                        <img src="{{ photo.file_path }}"
                             class="photo-thumbnail"
                             data-toggle="tooltip"
                             title="点击查看大图"
                             data-onclick="showFullImage(" data-event-id="f879f5d5"{{ photo.file_path }}', {{ photo.rating or 0 }})">
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- 照片查看模态框 -->
<div class="modal fade" id="imageModal" tabindex="-1" role="dialog" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">照片查看</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img id="fullImage" src="" class="img-fluid" alt="照片">
                <div id="imageRating" class="mt-2"></div>
            </div>
        </div>
    </div>
</div>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    // 初始化工具提示
    $(function () {
        $('[data-toggle="tooltip"]').tooltip();
        
        // 文件上传显示文件名
        $('.custom-file-input').on('change', function() {
            var fileCount = $(this)[0].files.length;
            if (fileCount > 0) {
                $(this).next('.custom-file-label').html(fileCount + ' 个文件已选择');
            } else {
                $(this).next('.custom-file-label').html('选择文件...');
            }
        });
    });
    
    // 显示大图
    function showFullImage(imageSrc, rating) {
        $('#fullImage').attr('src', imageSrc);
        
        // 构建星级评分HTML
        let ratingHtml = '';
        for (let i = 1; i <= 5; i++) {
            if (i <= rating) {
                ratingHtml += '<i class="fas fa-star text-warning"></i>';
            } else {
                ratingHtml += '<i class="far fa-star text-warning"></i>';
            }
        }
        
        $('#imageRating').html(ratingHtml);
        $('#imageModal').modal('show');
    }
</script>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}
