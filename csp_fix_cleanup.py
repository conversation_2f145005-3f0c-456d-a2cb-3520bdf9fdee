#!/usr/bin/env python3
"""
CSP修复清理工具
清理之前错误修复产生的重复属性
"""

import os
import re
from pathlib import Path

def clean_duplicate_attributes():
    """清理重复的data-onclick等属性"""
    
    print("🧹 CSP修复清理工具")
    print("=" * 40)
    
    template_dir = Path("app/templates")
    if not template_dir.exists():
        print("❌ 模板目录不存在")
        return
    
    fixed_files = []
    
    for html_file in template_dir.rglob("*.html"):
        try:
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 清理重复的data-onclick属性
            # 匹配类似: data-data-data-onclick="..." data-event-id="..." data-event-id="..."
            patterns = [
                # 清理重复的data-onclick
                (r'data-(?:data-)*onclick="([^"]*)"(?:\s+data-event-id="[^"]*")*', r'data-onclick="\1"'),
                # 清理重复的data-onchange
                (r'data-(?:data-)*onchange="([^"]*)"(?:\s+data-event-id="[^"]*")*', r'data-onchange="\1"'),
                # 清理重复的data-onsubmit
                (r'data-(?:data-)*onsubmit="([^"]*)"(?:\s+data-event-id="[^"]*")*', r'data-onsubmit="\1"'),
                # 清理重复的data-onload
                (r'data-(?:data-)*onload="([^"]*)"(?:\s+data-event-id="[^"]*")*', r'data-onload="\1"'),
                # 清理重复的data-onfocus
                (r'data-(?:data-)*onfocus="([^"]*)"(?:\s+data-event-id="[^"]*")*', r'data-onfocus="\1"'),
                # 清理重复的data-onblur
                (r'data-(?:data-)*onblur="([^"]*)"(?:\s+data-event-id="[^"]*")*', r'data-onblur="\1"'),
                # 清理重复的data-onmouseover
                (r'data-(?:data-)*onmouseover="([^"]*)"(?:\s+data-event-id="[^"]*")*', r'data-onmouseover="\1"'),
                # 清理重复的data-onmouseout
                (r'data-(?:data-)*onmouseout="([^"]*)"(?:\s+data-event-id="[^"]*")*', r'data-onmouseout="\1"'),
            ]
            
            for pattern, replacement in patterns:
                content = re.sub(pattern, replacement, content)
            
            # 如果内容有变化，写回文件
            if content != original_content:
                with open(html_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                rel_path = html_file.relative_to(Path("."))
                fixed_files.append(rel_path)
                print(f"✅ 清理文件: {rel_path}")
                
        except Exception as e:
            print(f"❌ 处理文件 {html_file} 时出错: {str(e)}")
    
    print(f"\n🎉 清理完成!")
    print(f"✅ 清理了 {len(fixed_files)} 个文件")
    
    if fixed_files:
        print("\n清理的文件:")
        for file_path in fixed_files:
            print(f"  - {file_path}")

def restore_from_backup():
    """从备份恢复文件"""
    
    print("📦 从备份恢复文件")
    print("=" * 40)
    
    backup_dir = Path("csp_fix_backup")
    if not backup_dir.exists():
        print("❌ 备份目录不存在")
        return False
    
    template_backup = backup_dir / "app" / "templates"
    template_dir = Path("app") / "templates"
    
    if not template_backup.exists():
        print("❌ 备份模板目录不存在")
        return False
    
    restored_files = []
    
    for backup_file in template_backup.rglob("*.html"):
        try:
            # 计算目标文件路径
            rel_path = backup_file.relative_to(template_backup)
            target_file = template_dir / rel_path
            
            # 确保目标目录存在
            target_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 复制文件
            import shutil
            shutil.copy2(backup_file, target_file)
            
            restored_files.append(rel_path)
            print(f"✅ 恢复文件: {rel_path}")
            
        except Exception as e:
            print(f"❌ 恢复文件 {backup_file} 时出错: {str(e)}")
    
    print(f"\n🎉 恢复完成!")
    print(f"✅ 恢复了 {len(restored_files)} 个文件")
    
    return len(restored_files) > 0

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='CSP修复清理工具')
    parser.add_argument('--restore', action='store_true', help='从备份恢复文件')
    parser.add_argument('--clean', action='store_true', help='清理重复属性')
    
    args = parser.parse_args()
    
    if args.restore:
        restore_from_backup()
    elif args.clean:
        clean_duplicate_attributes()
    else:
        print("请选择操作:")
        print("  --restore  从备份恢复文件")
        print("  --clean    清理重复属性")

if __name__ == "__main__":
    main()
