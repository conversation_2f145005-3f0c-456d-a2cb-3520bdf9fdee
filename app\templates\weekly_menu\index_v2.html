{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{{ url_for('weekly_menu_v2.plan') }}" class="d-none d-sm-inline-block btn btn-primary shadow-sm">
            <i class="fas fa-plus fa-sm text-white-50"></i> 新建周菜单
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">周菜单列表</h6>
        </div>
        <div class="card-body">
            <!-- 筛选表单 -->
            <form method="GET" action="{{ url_for('weekly_menu_v2.index') }}" class="mb-4">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <select name="area_id" class="form-control">
                            <option value="">-- 选择学校 --</option>
                            {% for area in areas %}
                                <option value="{{ area.id }}" {% if area_id == area.id %}selected{% endif %}>{{ area.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2 mb-2">
                        <select name="status" class="form-control">
                            <option value="">-- 状态 --</option>
                            <option value="计划中" {% if status == '计划中' %}selected{% endif %}>计划中</option>
                            <option value="已发布" {% if status == '已发布' %}selected{% endif %}>已发布</option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-2">
                        <input type="text" name="start_date" class="form-control datepicker" placeholder="开始日期" value="{{ start_date }}">
                    </div>
                    <div class="col-md-2 mb-2">
                        <input type="text" name="end_date" class="form-control datepicker" placeholder="结束日期" value="{{ end_date }}">
                    </div>
                    <div class="col-md-2 mb-2">
                        <input type="text" name="week" class="form-control" placeholder="周次 (如: 2023-W20)" value="{{ week }}">
                    </div>
                    <div class="col-md-1 mb-2">
                        <button type="submit" class="btn btn-primary btn-block">筛选</button>
                    </div>
                </div>
            </form>

            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>学校</th>
                            <th>周次</th>
                            <th>状态</th>
                            <th>创建者</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for menu in weekly_menus.items %}
                            <tr>
                                <td>{{ menu.id }}</td>
                                <td>{{ menu.area.name }}</td>
                                <td>{{ menu.week_display }}</td>
                                <td>
                                    <span class="badge badge-{{ menu.status|status_class }}">{{ menu.status }}</span>
                                </td>
                                <td>{{ menu.creator.name if menu.creator else '未知' }}</td>
                                <td>{{ menu.created_at|format_datetime }}</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ url_for('weekly_menu_v2.view', id=menu.id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> 查看
                                        </a>
                                        <a href="{{ url_for('weekly_menu_v2.print_menu', id=menu.id) }}" class="btn btn-sm btn-secondary" target="_blank" title="打印菜单">
                                            <i class="fas fa-print"></i> 打印
                                        </a>
                                        {% if menu.status == '计划中' %}
                                            <a href="{{ url_for('weekly_menu_v2.plan', area_id=menu.area_id, week_start=menu.week_start) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i> 编辑
                                            </a>
                                            <form method="POST" action="{{ url_for('weekly_menu_v2.publish', id=menu.id) }}" style="display: inline;">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                <button type="submit" class="btn btn-sm btn-success" data-data-data-data-data-onclick="return confirm(" data-event-id="c5e437ca" data-event-id="c5e437ca" data-event-id="c5e437ca" data-event-id="c5e437ca" data-event-id="c5e437ca"确定要发布这个周菜单吗？发布后将无法修改。')">
                                                    <i class="fas fa-check"></i> 发布
                                                </button>
                                            </form>
                                        {% endif %}
                                        {% if menu.status == '已发布' %}
                                            <a href="{{ url_for('purchase_order.create_from_menu', weekly_menu_id=menu.id) }}" style="cursor: pointer;" class="btn btn-sm btn-success">
                                                <i class="fas fa-shopping-cart"></i> 采购
                                            </a>
                                            <form method="POST" action="{{ url_for('weekly_menu_v2.unpublish', id=menu.id) }}" style="display: inline;">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                <button type="submit" class="btn btn-sm btn-warning" data-data-data-data-data-onclick="return confirm(" data-event-id="c5e437ca" data-event-id="c5e437ca" data-event-id="c5e437ca" data-event-id="c5e437ca" data-event-id="c5e437ca"确定要解除发布这个周菜单吗？解除后可以重新编辑。')">
                                                    <i class="fas fa-undo"></i> 解除发布
                                                </button>
                                            </form>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% else %}
                            <tr>
                                <td colspan="7" class="text-center">没有找到符合条件的周菜单</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if weekly_menus.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('weekly_menu_v2.index', page=weekly_menus.prev_num, area_id=area_id, status=status, start_date=start_date, end_date=end_date, week=week) }}" style="cursor: pointer;">上一页</a>
                        </li>
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">上一页</span>
                        </li>
                    {% endif %}

                    {% for page_num in weekly_menus.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                        {% if page_num %}
                            {% if page_num == weekly_menus.page %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                            {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('weekly_menu_v2.index', page=page_num, area_id=area_id, status=status, start_date=start_date, end_date=end_date, week=week) }}">{{ page_num }}</a>
                                </li>
                            {% endif %}
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if weekly_menus.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('weekly_menu_v2.index', page=weekly_menus.next_num, area_id=area_id, status=status, start_date=start_date, end_date=end_date, week=week) }}">下一页</a>
                        </li>
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">下一页</span>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
</div>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 初始化日期选择器
        $('.datepicker').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true,
            language: 'zh-CN'
        });
    });
</script>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}
