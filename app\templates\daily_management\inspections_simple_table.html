{% extends 'base.html' %}

{% block title %}检查记录 - {{ log.log_date|format_datetime('%Y-%m-%d') }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    /* 日期导航样式 */
    .date-navigation {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
        background-color: #f8f9fc;
        border-radius: 0.35rem;
        padding: 0.5rem 1rem;
        box-shadow: 0 0.15rem 0.5rem rgba(58, 59, 69, 0.15);
    }

    .date-navigation .date-display {
        font-size: 1.25rem;
        font-weight: 700;
        color: #4e73df;
        margin: 0;
    }

    .date-navigation .nav-buttons {
        display: flex;
        gap: 0.5rem;
    }

    /* 检查记录表格样式 */
    .inspection-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 1rem;
        border: 2px solid #36b9cc;
    }

    .inspection-table th,
    .inspection-table td {
        padding: 0.75rem;
        border: 1px solid #36b9cc;
        text-align: center;
        vertical-align: middle;
    }

    .inspection-table th {
        background-color: #e3f2fd;
        font-weight: 600;
        color: #2e59d9;
    }

    .inspection-table th.time-header {
        width: 80px;
        background-color: #e8f5e9;
    }

    /* 检查项目表头样式 */
    .inspection-table th.item-header {
        background-color: #fff3e0;
    }

    .inspection-table th.item-header:nth-child(2) {
        background-color: #ffe0b2; /* 地面卫生 */
    }

    .inspection-table th.item-header:nth-child(3) {
        background-color: #c8e6c9; /* 操作台卫生 */
    }

    .inspection-table th.item-header:nth-child(4) {
        background-color: #b3e5fc; /* 设备卫生 */
    }

    .inspection-table th.item-header:nth-child(5) {
        background-color: #d1c4e9; /* 食材存储 */
    }

    .inspection-table th.item-header:nth-child(6) {
        background-color: #ffcdd2; /* 人员卫生 */
    }

    .inspection-table th.item-header:nth-child(7) {
        background-color: #f8bbd0; /* 餐具消毒 */
    }

    /* 检查项目单元格样式 */
    .inspection-table td.item-cell {
        background-color: #fff3e0;
    }

    .inspection-table td.item-cell:nth-child(2) {
        background-color: #ffe0b2; /* 地面卫生 */
    }

    .inspection-table td.item-cell:nth-child(3) {
        background-color: #c8e6c9; /* 操作台卫生 */
    }

    .inspection-table td.item-cell:nth-child(4) {
        background-color: #b3e5fc; /* 设备卫生 */
    }

    .inspection-table td.item-cell:nth-child(5) {
        background-color: #d1c4e9; /* 食材存储 */
    }

    .inspection-table td.item-cell:nth-child(6) {
        background-color: #ffcdd2; /* 人员卫生 */
    }

    .inspection-table td.item-cell:nth-child(7) {
        background-color: #f8bbd0; /* 餐具消毒 */
    }

    /* 照片展示样式 */
    .photo-gallery {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        justify-content: center;
        margin-top: 0.5rem;
    }

    .photo-thumbnail {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 0.25rem;
        border: 1px solid #e3e6f0;
        cursor: pointer;
        transition: all 0.2s;
    }

    .photo-thumbnail:hover {
        transform: scale(1.05);
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    /* 检查情况样式 */
    .inspection-details {
        text-align: left;
        padding: 0.5rem;
    }

    .inspection-details .detail-label {
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .inspection-details .detail-content {
        margin-bottom: 0.5rem;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .date-navigation {
            flex-direction: column;
            gap: 0.5rem;
        }

        .inspection-table {
            display: block;
            overflow-x: auto;
        }
    }
</style>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 日期导航 -->
    <div class="date-navigation">
        <h1 class="date-display">检查记录 - {{ log.log_date|format_datetime('%Y-%m-%d') }}</h1>
        <div class="nav-buttons">
            {% if prev_log %}
            <a href="{{ url_for('daily_management.inspections_simple_table', log_id=prev_log.id) }}" class="btn btn-primary btn-sm">
                <i class="fas fa-chevron-left mr-1"></i> 前一天
            </a>
            {% else %}
            <button class="btn btn-primary btn-sm disabled" disabled>
                <i class="fas fa-chevron-left mr-1"></i> 前一天
            </button>
            {% endif %}

            <div class="btn-group">
                <a href="{{ url_for('daily_management.edit_log', date_str=log.log_date|format_datetime('%Y-%m-%d')) }}" class="btn btn-info btn-sm">
                    <i class="fas fa-calendar-day mr-1"></i> 日志详情
                </a>
                <button type="button" class="btn btn-info btn-sm dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span class="sr-only">切换下拉菜单</span>
                </button>
                <div class="dropdown-menu dropdown-menu-right">
                    <form class="px-3 py-2" data-data-data-data-data-onsubmit="goToDate(event)" data-event-id="1f21e4b2" data-event-id="1f21e4b2" data-event-id="1f21e4b2" data-event-id="1f21e4b2" data-event-id="1f21e4b2">
                        <div class="form-group mb-2">
                            <label for="dateInput">选择日期</label>
                            <input type="date" class="form-control" id="dateInput" value="{{ log.log_date|format_datetime('%Y-%m-%d') }}">
                        </div>
                        <button type="submit" class="btn btn-primary btn-sm btn-block">
                            <i class="fas fa-search mr-1"></i> 查看
                        </button>
                    </form>
                </div>
            </div>

            {% if next_log %}
            <a href="{{ url_for('daily_management.inspections_simple_table', log_id=next_log.id) }}" class="btn btn-primary btn-sm">
                后一天 <i class="fas fa-chevron-right ml-1"></i>
            </a>
            {% else %}
            <button class="btn btn-primary btn-sm disabled" disabled>
                后一天 <i class="fas fa-chevron-right ml-1"></i>
            </button>
            {% endif %}
        </div>
    </div>

    <!-- 检查记录表格 -->
    <table class="inspection-table">
        <thead>
            <tr>
                <th class="time-header">时间</th>
                <th class="item-header">地面卫生</th>
                <th class="item-header">操作台卫生</th>
                <th class="item-header">设备卫生</th>
                <th class="item-header">食材存储</th>
                <th class="item-header">人员卫生</th>
                <th class="item-header">餐具消毒</th>
            </tr>
        </thead>
        <tbody>
            <!-- 晨检 -->
            <tr>
                <td class="time-header">晨检</td>
                {% for item in ['地面卫生', '操作台卫生', '设备卫生', '食材存储', '人员卫生', '餐具消毒'] %}
                    {% set inspection = morning_inspections|selectattr('inspection_item', 'equalto', item)|first %}
                    <td class="item-cell">
                        {% if inspection %}
                            <div class="text-center">可分页显示照片</div>
                            <div class="inspection-details">
                                <div class="detail-label">检查情况：</div>
                                <div class="detail-content">
                                    {% if inspection.description %}
                                        {{ inspection.description }}
                                    {% else %}
                                        无描述
                                    {% endif %}
                                </div>
                                <div class="detail-label">评分：</div>
                                <div class="detail-content">
                                    {% if inspection.photos and inspection.photos|length > 0 and inspection.photos[0].rating %}
                                        {{ inspection.photos[0].rating }} / 5
                                    {% else %}
                                        未评分
                                    {% endif %}
                                </div>
                            </div>
                        {% else %}
                            <div class="text-muted">未检查</div>
                        {% endif %}
                    </td>
                {% endfor %}
            </tr>

            <!-- 午检 -->
            <tr>
                <td class="time-header">午检</td>
                {% for item in ['地面卫生', '操作台卫生', '设备卫生', '食材存储', '人员卫生', '餐具消毒'] %}
                    {% set inspection = noon_inspections|selectattr('inspection_item', 'equalto', item)|first %}
                    <td class="item-cell">
                        {% if inspection %}
                            <div class="text-center">可分页显示照片</div>
                            <div class="inspection-details">
                                <div class="detail-label">检查情况：</div>
                                <div class="detail-content">
                                    {% if inspection.description %}
                                        {{ inspection.description }}
                                    {% else %}
                                        无描述
                                    {% endif %}
                                </div>
                                <div class="detail-label">评分：</div>
                                <div class="detail-content">
                                    {% if inspection.photos and inspection.photos|length > 0 and inspection.photos[0].rating %}
                                        {{ inspection.photos[0].rating }} / 5
                                    {% else %}
                                        未评分
                                    {% endif %}
                                </div>
                            </div>
                        {% else %}
                            <div class="text-muted">未检查</div>
                        {% endif %}
                    </td>
                {% endfor %}
            </tr>

            <!-- 晚检 -->
            <tr>
                <td class="time-header">晚检</td>
                {% for item in ['地面卫生', '操作台卫生', '设备卫生', '食材存储', '人员卫生', '餐具消毒'] %}
                    {% set inspection = evening_inspections|selectattr('inspection_item', 'equalto', item)|first %}
                    <td class="item-cell">
                        {% if inspection %}
                            <div class="text-center">可分页显示照片</div>
                            <div class="inspection-details">
                                <div class="detail-label">检查情况：</div>
                                <div class="detail-content">
                                    {% if inspection.description %}
                                        {{ inspection.description }}
                                    {% else %}
                                        无描述
                                    {% endif %}
                                </div>
                                <div class="detail-label">评分：</div>
                                <div class="detail-content">
                                    {% if inspection.photos and inspection.photos|length > 0 and inspection.photos[0].rating %}
                                        {{ inspection.photos[0].rating }} / 5
                                    {% else %}
                                        未评分
                                    {% endif %}
                                </div>
                            </div>
                        {% else %}
                            <div class="text-muted">未检查</div>
                        {% endif %}
                    </td>
                {% endfor %}
            </tr>
        </tbody>
    </table>

    <!-- 底部导航 -->
    <div class="d-flex justify-content-between mb-4">
        <div>
            <a href="{{ url_for('daily_management.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-home mr-1"></i> 返回首页
            </a>
            <a href="{{ url_for('daily_management.logs') }}" class="btn btn-info btn-sm ml-2">
                <i class="fas fa-list mr-1"></i> 所有日志
            </a>
        </div>
        <div>
            <a href="{{ url_for('daily_management.inspections', log_id=log.id) }}" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-th-large mr-1"></i> 原卡片视图
            </a>
            <a href="{{ url_for('daily_management.inspections_table', log_id=log.id) }}" class="btn btn-outline-primary btn-sm ml-2">
                <i class="fas fa-table mr-1"></i> 表格视图
            </a>
            <a href="{{ url_for('daily_management.inspections_card_layout', log_id=log.id) }}" class="btn btn-outline-primary btn-sm ml-2">
                <i class="fas fa-th mr-1"></i> 卡片布局
            </a>
        </div>
    </div>
</div>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    // 初始化工具提示
    $(function () {
        $('[data-toggle="tooltip"]').tooltip();
    });

    // 跳转到指定日期
    function goToDate(event) {
        event.preventDefault();
        const dateInput = document.getElementById('dateInput');
        const selectedDate = dateInput.value;

        if (selectedDate) {
            window.location.href = "{{ url_for('daily_management.inspections_by_date', date_str='') }}" + selectedDate + "?view=simple_table";
        }
    }

    // 键盘导航
    document.addEventListener('keydown', function(e) {
        // 左箭头键 - 前一天
        if (e.keyCode === 37) {
            const prevBtn = document.querySelector('.nav-buttons a:first-child');
            if (prevBtn && !prevBtn.classList.contains('disabled')) {
                prevBtn.click();
            }
        }
        // 右箭头键 - 后一天
        else if (e.keyCode === 39) {
            const nextBtn = document.querySelector('.nav-buttons a:last-child');
            if (nextBtn && !nextBtn.classList.contains('disabled')) {
                nextBtn.click();
            }
        }
    });
</script>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}
