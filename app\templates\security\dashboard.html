{% extends "base.html" %}

{% block title %}安全仪表盘{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1>安全仪表盘</h1>
                <p class="text-muted">监控和管理系统安全状态</p>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ total_blocked }}</h4>
                            <p class="mb-0">被阻止的IP</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-ban fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="active-sessions">-</h4>
                            <p class="mb-0">活跃会话</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="total-requests">-</h4>
                            <p class="mb-0">总请求数</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>在线</h4>
                            <p class="mb-0">系统状态</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-shield-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 被阻止的IP -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>被阻止的IP地址</h5>
                    <button class="btn btn-sm btn-outline-primary float-right" data-data-data-data-data-data-onclick="refreshBlockedIPs()" data-event-id="7b406a70" data-event-id="7b406a70" data-event-id="7b406a70" data-event-id="7b406a70" data-event-id="7b406a70" data-event-id="7b406a70">
                        <i class="fas fa-sync"></i> 刷新
                    </button>
                </div>
                <div class="card-body">
                    <div id="blocked-ips-list">
                        {% if blocked_ips %}
                            {% for ip in blocked_ips %}
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="font-monospace">{{ ip }}</span>
                                <button class="btn btn-sm btn-outline-success" data-data-data-data-data-onclick="unblockIP(" data-event-id="78f4a093" data-event-id="78f4a093" data-event-id="78f4a093" data-event-id="78f4a093" data-event-id="78f4a093"{{ ip }}')">
                                    解除阻止
                                </button>
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted">暂无被阻止的IP</p>
                        {% endif %}
                    </div>
                    
                    <!-- 手动阻止IP -->
                    <hr>
                    <div class="input-group">
                        <input type="text" class="form-control" id="block-ip-input" placeholder="输入要阻止的IP地址">
                        <div class="input-group-append">
                            <button class="btn btn-danger" data-data-data-data-data-data-onclick="blockIP()" data-event-id="576f259b" data-event-id="576f259b" data-event-id="576f259b" data-event-id="576f259b" data-event-id="576f259b" data-event-id="576f259b">阻止</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 访问频率最高的IP -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>访问频率最高的IP</h5>
                    <button class="btn btn-sm btn-outline-primary float-right" data-data-data-data-data-data-onclick="refreshTopIPs()" data-event-id="7aec65fb" data-event-id="7aec65fb" data-event-id="7aec65fb" data-event-id="7aec65fb" data-event-id="7aec65fb" data-event-id="7aec65fb">
                        <i class="fas fa-sync"></i> 刷新
                    </button>
                </div>
                <div class="card-body">
                    <div id="top-ips-list">
                        {% for ip, stats in top_ips %}
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="font-monospace">{{ ip }}</span>
                            <span class="badge badge-info">{{ stats.count }} 次</span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作按钮 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>安全操作</h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-warning" data-data-data-data-data-data-onclick="clearLogs()" data-event-id="88cf09db" data-event-id="88cf09db" data-event-id="88cf09db" data-event-id="88cf09db" data-event-id="88cf09db" data-event-id="88cf09db">
                        <i class="fas fa-trash"></i> 清空访问日志
                    </button>
                    <button class="btn btn-info" data-data-data-data-data-data-onclick="viewAccessLog()" data-event-id="e7a6b860" data-event-id="e7a6b860" data-event-id="e7a6b860" data-event-id="e7a6b860" data-event-id="e7a6b860" data-event-id="e7a6b860">
                        <i class="fas fa-list"></i> 查看访问日志
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script nonce="{{ csp_nonce }}">
// 刷新被阻止的IP列表
function refreshBlockedIPs() {
    $.get('/security/blocked-ips', function(data) {
        let html = '';
        if (data.blocked_ips.length > 0) {
            data.blocked_ips.forEach(function(ip) {
                html += `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="font-monospace">${ip}</span>
                        <button class="btn btn-sm btn-outline-success" data-data-data-data-data-onclick="unblockIP(" data-event-id="78f4a093" data-event-id="78f4a093" data-event-id="78f4a093" data-event-id="78f4a093" data-event-id="78f4a093"${ip}')">
                            解除阻止
                        </button>
                    </div>
                `;
            });
        } else {
            html = '<p class="text-muted">暂无被阻止的IP</p>';
        }
        $('#blocked-ips-list').html(html);
    });
}

// 解除IP阻止
function unblockIP(ip) {
    if (confirm(`确定要解除对 ${ip} 的阻止吗？`)) {
        $.post('/security/unblock-ip', 
            JSON.stringify({ip: ip}), 
            function(data) {
                if (data.success) {
                    alert(data.message);
                    refreshBlockedIPs();
                } else {
                    alert('操作失败: ' + data.error);
                }
            }, 'json'
        ).fail(function() {
            alert('网络错误');
        });
    }
}

// 阻止IP
function blockIP() {
    const ip = $('#block-ip-input').val().trim();
    if (!ip) {
        alert('请输入IP地址');
        return;
    }
    
    if (confirm(`确定要阻止 ${ip} 吗？`)) {
        $.post('/security/block-ip', 
            JSON.stringify({ip: ip}), 
            function(data) {
                if (data.success) {
                    alert(data.message);
                    $('#block-ip-input').val('');
                    refreshBlockedIPs();
                } else {
                    alert('操作失败: ' + data.error);
                }
            }, 'json'
        ).fail(function() {
            alert('网络错误');
        });
    }
}

// 刷新统计信息
function refreshStats() {
    $.get('/security/stats', function(data) {
        $('#active-sessions').text(data.active_sessions);
        $('#total-requests').text(data.total_requests);
    });
}

// 清空日志
function clearLogs() {
    if (confirm('确定要清空所有访问日志吗？此操作不可恢复。')) {
        $.post('/security/clear-logs', {}, function(data) {
            if (data.success) {
                alert(data.message);
                refreshStats();
            } else {
                alert('操作失败: ' + data.error);
            }
        }, 'json');
    }
}

// 查看访问日志
function viewAccessLog() {
    window.open('/security/access-log', '_blank');
}

// 页面加载时刷新统计信息
$(document).ready(function() {
    refreshStats();
    
    // 每30秒自动刷新统计信息
    setInterval(refreshStats, 30000);
});
</script>
{% endblock %}

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>