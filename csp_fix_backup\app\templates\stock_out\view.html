{% extends 'base.html' %}

{% block title %}查看出库单{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">出库单详情</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('stock_out.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                        {% if stock_out.status == '待审核' %}
                        <a href="{{ url_for('stock_out.edit', id=stock_out.id) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> 编辑
                        </a>
                        {% endif %}
                        <button type="button" class="btn btn-primary btn-sm" data-data-data-data-onclick="printStockOut({{ stock_out.id }})" data-event-id="7039fde4" data-event-id="7039fde4" data-event-id="7039fde4" data-event-id="7039fde4">
                            <i class="fas fa-print"></i> 打印出库单
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">出库单号</th>
                                    <td>{{ stock_out.stock_out_number }}</td>
                                </tr>
                                <tr>
                                    <th>仓库</th>
                                    <td>{{ stock_out.warehouse.name }}</td>
                                </tr>
                                <tr>
                                    <th>出库日期</th>
                                    <td>{{  stock_out.stock_out_date|format_datetime('%Y-%m-%d')  }}</td>
                                </tr>
                                <tr>
                                    <th>出库类型</th>
                                    <td>{{ stock_out.stock_out_type }}</td>
                                </tr>
                                {% if stock_out.recipient %}
                                <tr>
                                    <th>领用人</th>
                                    <td>{{ stock_out.recipient }}</td>
                                </tr>
                                {% endif %}
                                {% if stock_out.department %}
                                <tr>
                                    <th>领用部门</th>
                                    <td>{{ stock_out.department }}</td>
                                </tr>
                                {% endif %}
                                {% if consumption_plan %}
                                <tr>
                                    <th>关联消耗计划</th>
                                    <td>
                                        <a href="{{ url_for('consumption_plan.view', id=consumption_plan.id) }}">
                                            {{ consumption_plan.id }}
                                            {% if menu_plan %}
                                            ({{ menu_plan.plan_date }} {{ menu_plan.meal_type }})
                                            {% endif %}
                                        </a>
                                    </td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">操作人</th>
                                    <td>{{ stock_out.operator.real_name or stock_out.operator.username }}</td>
                                </tr>
                                <tr>
                                    <th>审批人</th>
                                    <td>{{ stock_out.approver.real_name or stock_out.approver.username if stock_out.approver else '-' }}</td>
                                </tr>
                                <tr>
                                    <th>状态</th>
                                    <td>
                                        {% if stock_out.status == '待审核' %}
                                        <span class="badge badge-warning">待审核</span>
                                        {% elif stock_out.status == '已审核' %}
                                        <span class="badge badge-info">已审核</span>
                                        {% elif stock_out.status == '已出库' %}
                                        <span class="badge badge-success">已出库</span>
                                        {% elif stock_out.status == '已取消' %}
                                        <span class="badge badge-danger">已取消</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>备注</th>
                                    <td>{{ stock_out.notes or '-' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- 出库明细列表 -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h4 class="card-title">出库明细列表</h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>序号</th>
                                            <th>食材名称</th>
                                            <th>批次号</th>
                                            <th>出库数量</th>
                                            <th>单位</th>
                                            <th>备注</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in stock_out_items %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>{{ item.ingredient.name }}</td>
                                            <td>
                                                <span>{{ item.batch_number }}</span>
                                            </td>
                                            <td>{{ item.quantity }}</td>
                                            <td>{{ item.unit }}</td>
                                            <td>{{ item.notes or '-' }}</td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-info" data-toggle="collapse" data-target="#traceability-{{ item.id }}">
                                                    <i class="fas fa-search"></i> 查看溯源
                                                </button>
                                            </td>
                                        </tr>
                                        <!-- 溯源信息折叠面板 -->
                                        <tr class="collapse" id="traceability-{{ item.id }}">
                                            <td colspan="7">
                                                <div class="card">
                                                    <div class="card-header bg-info">
                                                        <h5 class="card-title text-white">批次号 {{ item.batch_number }} 溯源信息</h5>
                                                    </div>
                                                    <div class="card-body">
                                                        {% set batch_info = batch_traceability[item.batch_number] %}

                                                        <!-- 出库单信息 -->
                                                        <div class="card mb-3">
                                                            <div class="card-header bg-danger">
                                                                <h6 class="card-title">出库单信息</h6>
                                                            </div>
                                                            <div class="card-body">
                                                                <div class="row">
                                                                    <div class="col-md-6">
                                                                        <table class="table table-bordered">
                                                                            <tr>
                                                                                <th class="w-30">出库单号</th>
                                                                                <td>{{ stock_out.stock_out_number }}</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <th>出库日期</th>
                                                                                <td>{{ stock_out.stock_out_date|format_datetime('%Y-%m-%d') }}</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <th>出库类型</th>
                                                                                <td>{{ stock_out.stock_out_type }}</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <th>仓库</th>
                                                                                <td>{{ stock_out.warehouse.name }}</td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <table class="table table-bordered">
                                                                            <tr>
                                                                                <th class="w-30">领用人</th>
                                                                                <td>{{ stock_out.recipient or '-' }}</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <th>领用部门</th>
                                                                                <td>{{ stock_out.department or '-' }}</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <th>操作人</th>
                                                                                <td>{{ stock_out.operator.real_name if stock_out.operator else '-' }}</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <th>出库状态</th>
                                                                                <td>
                                                                                    {% if stock_out.status == '已出库' %}
                                                                                    <span class="badge badge-success">已出库</span>
                                                                                    {% elif stock_out.status == '待审核' %}
                                                                                    <span class="badge badge-warning">待审核</span>
                                                                                    {% elif stock_out.status == '已审核' %}
                                                                                    <span class="badge badge-info">已审核</span>
                                                                                    {% elif stock_out.status == '已取消' %}
                                                                                    <span class="badge badge-danger">已取消</span>
                                                                                    {% else %}
                                                                                    {{ stock_out.status }}
                                                                                    {% endif %}
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- 供应商信息 -->
                                                        {% if batch_info.supplier %}
                                                        <div class="card mb-3">
                                                            <div class="card-header bg-primary">
                                                                <h6 class="card-title">供应商信息</h6>
                                                            </div>
                                                            <div class="card-body">
                                                                <div class="row">
                                                                    <div class="col-md-6">
                                                                        <table class="table table-bordered">
                                                                            <tr>
                                                                                <th class="w-30">供应商名称</th>
                                                                                <td>
                                                                                    <a href="{{ url_for('supplier.view', id=batch_info.supplier.id) }}" target="_blank">
                                                                                        {{ batch_info.supplier.name }}
                                                                                    </a>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <th>法定代表人</th>
                                                                                <td>{{ batch_info.supplier.legal_representative or '-' }}</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <th>联系电话</th>
                                                                                <td>{{ batch_info.supplier.phone or '-' }}</td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <table class="table table-bordered">
                                                                            <tr>
                                                                                <th class="w-30">地址</th>
                                                                                <td>{{ batch_info.supplier.address or '-' }}</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <th>营业执照号</th>
                                                                                <td>{{ batch_info.supplier.business_license or '-' }}</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <th>备注</th>
                                                                                <td>{{ batch_info.supplier.notes or '-' }}</td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        {% endif %}

                                                        <!-- 入库信息 -->
                                                        <div class="card mb-3">
                                                            <div class="card-header bg-success">
                                                                <h6 class="card-title">入库信息</h6>
                                                            </div>
                                                            <div class="card-body">
                                                                {% if batch_info.stock_in_items %}
                                                                <div class="table-responsive">
                                                                    <table class="table table-bordered table-striped">
                                                                        <thead>
                                                                            <tr>
                                                                                <th>入库单号</th>
                                                                                <th>入库日期</th>
                                                                                <th>入库类型</th>
                                                                                <th>入库数量</th>
                                                                                <th>操作人</th>
                                                                                <th>入库状态</th>
                                                                            </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                            {% for item in batch_info.stock_in_items %}
                                                                            <tr>
                                                                                <td>
                                                                                    {% if item.stock_in %}
                                                                                    <a href="{{ url_for('stock_in.view', id=item.stock_in.id) }}">
                                                                                        {{ item.stock_in.stock_in_number }}
                                                                                    </a>
                                                                                    {% else %}
                                                                                    -
                                                                                    {% endif %}
                                                                                </td>
                                                                                <td>{{ item.stock_in.stock_in_date|format_datetime('%Y-%m-%d') if item.stock_in and item.stock_in.stock_in_date else '-' }}</td>
                                                                                <td>{{ item.stock_in.stock_in_type if item.stock_in else '-' }}</td>
                                                                                <td>{{ item.quantity }} {{ item.unit }}</td>
                                                                                <td>{{ item.stock_in.operator.real_name if item.stock_in and item.stock_in.operator else '-' }}</td>
                                                                                <td>
                                                                                    {% if item.stock_in and item.stock_in.status == '已入库' %}
                                                                                    <span class="badge badge-success">已入库</span>
                                                                                    {% elif item.stock_in and item.stock_in.status == '待审核' %}
                                                                                    <span class="badge badge-warning">待审核</span>
                                                                                    {% elif item.stock_in and item.stock_in.status == '已审核' %}
                                                                                    <span class="badge badge-info">已审核</span>
                                                                                    {% elif item.stock_in and item.stock_in.status == '已取消' %}
                                                                                    <span class="badge badge-danger">已取消</span>
                                                                                    {% else %}
                                                                                    -
                                                                                    {% endif %}
                                                                                </td>
                                                                            </tr>
                                                                            {% endfor %}
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                                {% else %}
                                                                <div class="alert alert-info">
                                                                    <i class="fas fa-info-circle"></i> 未找到该批次号的入库记录。
                                                                </div>
                                                                {% endif %}
                                                            </div>
                                                        </div>

                                                        <!-- 检验检疫证明 -->
                                                        <div class="card mb-3">
                                                            <div class="card-header bg-warning">
                                                                <h6 class="card-title">检验检疫证明</h6>
                                                            </div>
                                                            <div class="card-body">
                                                                {% if batch_info.certificates %}
                                                                <div class="table-responsive">
                                                                    <table class="table table-bordered table-striped">
                                                                        <thead>
                                                                            <tr>
                                                                                <th>证书类型</th>
                                                                                <th>证书编号</th>
                                                                                <th>发证日期</th>
                                                                                <th>证书文件</th>
                                                                                <th>备注</th>
                                                                            </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                            {% for cert in batch_info.certificates %}
                                                                            <tr>
                                                                                <td>{{ cert.document_type }}</td>
                                                                                <td>{{ cert.certificate_number if hasattr(cert, 'certificate_number') else cert.document_no if hasattr(cert, 'document_no') else '-' }}</td>
                                                                                <td>{{ cert.issue_date|format_datetime('%Y-%m-%d') if hasattr(cert, 'issue_date') and cert.issue_date else '-' }}</td>
                                                                                <td>
                                                                                    {% if hasattr(cert, 'file_path') and cert.file_path %}
                                                                                    <a href="{{ url_for('static', filename=cert.file_path) }}" target="_blank" class="btn btn-sm btn-primary">
                                                                                        <i class="fas fa-file-pdf"></i> 查看文件
                                                                                    </a>
                                                                                    {% elif hasattr(cert, 'document_path') and cert.document_path %}
                                                                                    <a href="{{ url_for('static', filename=cert.document_path) }}" target="_blank" class="btn btn-sm btn-primary">
                                                                                        <i class="fas fa-file-pdf"></i> 查看文件
                                                                                    </a>
                                                                                    {% else %}
                                                                                    -
                                                                                    {% endif %}
                                                                                </td>
                                                                                <td>{{ cert.notes if hasattr(cert, 'notes') else cert.remark if hasattr(cert, 'remark') else '-' }}</td>
                                                                            </tr>
                                                                            {% endfor %}
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                                {% else %}
                                                                <div class="alert alert-info">
                                                                    <i class="fas fa-info-circle"></i> 未找到该批次号的检验检疫证明。
                                                                </div>
                                                                {% endif %}
                                                            </div>
                                                        </div>

                                                        <!-- 食材溯源信息 -->
                                                        {% if batch_info.material_batch %}
                                                        <div class="card mb-3">
                                                            <div class="card-header bg-primary">
                                                                <h6 class="card-title">食材溯源信息</h6>
                                                            </div>
                                                            <div class="card-body">
                                                                <div class="row">
                                                                    <div class="col-md-6">
                                                                        <table class="table table-bordered">
                                                                            <tr>
                                                                                <th class="w-30">批次编号</th>
                                                                                <td>{{ batch_info.material_batch.batch_number }}</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <th>食材名称</th>
                                                                                <td>{{ batch_info.material_batch.ingredient.name }}</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <th>供应商</th>
                                                                                <td>{{ batch_info.material_batch.supplier.name }}</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <th>生产日期</th>
                                                                                <td>{{ batch_info.material_batch.production_date|format_datetime('%Y-%m-%d') }}</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <th>过期日期</th>
                                                                                <td>{{ batch_info.material_batch.expiry_date|format_datetime('%Y-%m-%d') }}</td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <table class="table table-bordered">
                                                                            <tr>
                                                                                <th class="w-30">生产批号</th>
                                                                                <td>{{ batch_info.material_batch.production_batch_no or '-' }}</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <th>产地信息</th>
                                                                                <td>{{ batch_info.material_batch.origin_place or '-' }}</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <th>检验编号</th>
                                                                                <td>{{ batch_info.material_batch.inspection_no or '-' }}</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <th>合格证编号</th>
                                                                                <td>{{ batch_info.material_batch.certificate_no or '-' }}</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <th>状态</th>
                                                                                <td>{{ batch_info.material_batch.status }}</td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        {% endif %}

                                                        <!-- 溯源文档 -->
                                                        {% if batch_info.trace_documents %}
                                                        <div class="card mb-3">
                                                            <div class="card-header bg-info">
                                                                <h6 class="card-title">溯源文档</h6>
                                                            </div>
                                                            <div class="card-body">
                                                                <div class="table-responsive">
                                                                    <table class="table table-bordered table-striped">
                                                                        <thead>
                                                                            <tr>
                                                                                <th>文档类型</th>
                                                                                <th>文档编号</th>
                                                                                <th>上传时间</th>
                                                                                <th>上传人</th>
                                                                                <th>文档</th>
                                                                                <th>备注</th>
                                                                            </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                            {% for doc in batch_info.trace_documents %}
                                                                            <tr>
                                                                                <td>{{ doc.document_type }}</td>
                                                                                <td>{{ doc.document_no or '-' }}</td>
                                                                                <td>{{ doc.upload_time|format_datetime('%Y-%m-%d %H:%M') }}</td>
                                                                                <td>{{ doc.uploader.real_name if doc.uploader else '-' }}</td>
                                                                                <td>
                                                                                    {% if doc.document_path %}
                                                                                    <a href="{{ url_for('static', filename=doc.document_path) }}" target="_blank" class="btn btn-sm btn-primary">
                                                                                        <i class="fas fa-file-pdf"></i> 查看文件
                                                                                    </a>
                                                                                    {% else %}
                                                                                    -
                                                                                    {% endif %}
                                                                                </td>
                                                                                <td>{{ doc.remark or '-' }}</td>
                                                                            </tr>
                                                                            {% endfor %}
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        {% endif %}

                                                        <!-- 批次流水 -->
                                                        {% if batch_info.batch_flows %}
                                                        <div class="card mb-3">
                                                            <div class="card-header bg-secondary">
                                                                <h6 class="card-title">批次流水</h6>
                                                            </div>
                                                            <div class="card-body">
                                                                <div class="table-responsive">
                                                                    <table class="table table-bordered table-striped">
                                                                        <thead>
                                                                            <tr>
                                                                                <th>流水类型</th>
                                                                                <th>流向</th>
                                                                                <th>数量</th>
                                                                                <th>单位</th>
                                                                                <th>关联单据</th>
                                                                                <th>操作人</th>
                                                                                <th>流水日期</th>
                                                                                <th>备注</th>
                                                                            </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                            {% for flow in batch_info.batch_flows %}
                                                                            <tr>
                                                                                <td>{{ flow.flow_type }}</td>
                                                                                <td>
                                                                                    {% if flow.flow_direction == '增加' %}
                                                                                    <span class="text-success">{{ flow.flow_direction }}</span>
                                                                                    {% else %}
                                                                                    <span class="text-danger">{{ flow.flow_direction }}</span>
                                                                                    {% endif %}
                                                                                </td>
                                                                                <td>{{ flow.quantity }}</td>
                                                                                <td>{{ flow.unit }}</td>
                                                                                <td>
                                                                                    {% if flow.related_id and flow.related_type %}
                                                                                    {% if flow.related_type == '入库单' %}
                                                                                    <a href="{{ url_for('stock_in.view', id=flow.related_id) }}">{{ flow.related_type }}#{{ flow.related_id }}</a>
                                                                                    {% elif flow.related_type == '出库单' %}
                                                                                    <a href="{{ url_for('stock_out.view', id=flow.related_id) }}">{{ flow.related_type }}#{{ flow.related_id }}</a>
                                                                                    {% elif flow.related_type == '消耗计划' %}
                                                                                    <a href="{{ url_for('consumption_plan.view', id=flow.related_id) }}">{{ flow.related_type }}#{{ flow.related_id }}</a>
                                                                                    {% else %}
                                                                                    {{ flow.related_type }}#{{ flow.related_id }}
                                                                                    {% endif %}
                                                                                    {% else %}
                                                                                    -
                                                                                    {% endif %}
                                                                                </td>
                                                                                <td>{{ flow.operator.real_name if flow.operator else '-' }}</td>
                                                                                <td>{{ flow.flow_date|format_datetime('%Y-%m-%d %H:%M') }}</td>
                                                                                <td>{{ flow.remark or '-' }}</td>
                                                                            </tr>
                                                                            {% endfor %}
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="7" class="text-center">暂无出库明细</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    {% if consumption_plan %}
                    <div class="card mb-3">
                        <div class="card-header">关联消耗计划信息</div>
                        <div class="card-body">
                            <div class="row">
                                <!-- 左侧：消耗计划信息 -->
                                <div class="col-md-6">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item"><b>消耗计划ID：</b> {{ consumption_plan.id }}</li>
                                        <li class="list-group-item"><b>消耗日期：</b> {{ consumption_plan.consumption_date|format_datetime('%Y-%m-%d') if consumption_plan.consumption_date else '' }}</li>
                                        <li class="list-group-item"><b>餐次：</b> {{ consumption_plan.meal_type }}</li>
                                        <li class="list-group-item"><b>用餐人数：</b> {{ consumption_plan.diners_count if consumption_plan.diners_count is not none else '-' }}</li>
                                        <li class="list-group-item"><b>学校名称：</b>
                                            {% if consumption_plan and consumption_plan.menu_plan and consumption_plan.menu_plan.area and consumption_plan.menu_plan.area.name %}
                                                {{ consumption_plan.menu_plan.area.name }}
                                            {% elif current_user.get_accessible_areas() %}
                                                {{ current_user.get_accessible_areas()[0].name }} {# Display the first accessible area's name as fallback #}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </li>
                                    </ul>
                                    <a class="btn btn-link mt-2" href="{{ url_for('food_trace.index', consumption_plan_id=consumption_plan.id) }}">查看溯源链路</a>
                                </div>

                                <!-- 右侧：当天当餐次的食谱和食材信息 -->
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3">
                                        <i class="fas fa-utensils mr-2"></i>
                                        {{ consumption_plan.consumption_date|format_datetime('%Y-%m-%d') if consumption_plan.consumption_date else '' }}
                                        {{ consumption_plan.meal_type }} 食谱及食材
                                    </h6>

                                    {% if recipes_with_ingredients %}
                                    <div class="table-responsive">
                                        <table class="table table-sm table-bordered">
                                            <thead style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; font-size: 12px;">
                                                <tr>
                                                    <th style="width: 40%;">食谱名称</th>
                                                    <th style="width: 60%;">主要食材（除调味品）</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for recipe_info in recipes_with_ingredients %}
                                                <tr>
                                                    <td style="font-weight: 500; color: #2c3e50; font-size: 13px;">
                                                        {{ recipe_info.recipe_name }}
                                                        {% if recipe_info.recipe_category %}
                                                        <br><small class="text-muted">{{ recipe_info.recipe_category }}</small>
                                                        {% endif %}
                                                    </td>
                                                    <td style="font-size: 12px;">
                                                        {% if recipe_info.main_ingredients %}
                                                            {% for ingredient in recipe_info.main_ingredients %}
                                                            <span class="badge badge-light mr-1 mb-1" style="font-size: 11px; color: #495057;">{{ ingredient }}</span>
                                                            {% endfor %}
                                                        {% else %}
                                                            <span class="text-muted">无食材信息</span>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <div class="alert alert-info alert-sm">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        <small>未找到该日期餐次的食谱信息</small>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    {# Display Recipes associated with the linked Consumption Plan's Menu Plan #}
                    {% if recipes %}
                    <div class="card mt-4">
                        <div class="card-header">
                            <h4 class="card-title">关联菜单菜谱</h4>
                        </div>
                        <div class="card-body">
                            <ul class="list-group">
                                {% for recipe in recipes %}
                                <li class="list-group-item">{{ recipe.name }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    {% elif consumption_plan and consumption_plan.menu_plan %}
                    {# Only show message if a consumption plan and menu plan are linked but no recipes found #}
                    <div class="card mt-4">
                        <div class="card-header">
                            <h4 class="card-title">关联菜单菜谱</h4>
                        </div>
                        <div class="card-body">
                            <p>该关联菜单计划下没有找到菜谱。</p>
                        </div>
                    </div>
                    {% elif consumption_plan %}
                    {# Only show message if a consumption plan is linked but no menu plan found #}
                    <div class="card mt-4">
                        <div class="card-header">
                            <h4 class="card-title">关联菜单菜谱</h4>
                        </div>
                        <div class="card-body">
                            <p>关联的消耗计划没有找到菜单计划。</p>
                        </div>
                    </div>
                    {% endif %}

                    <!-- 食材溯源信息 -->
                    {% if ingredient_traceability %}
                    <div class="card mt-4">
                        <div class="card-header">
                            <h4 class="card-title"><i class="fas fa-search-location mr-2"></i>食材溯源信息 - 用在哪一天哪一餐哪个食谱</h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                        <tr>
                                            <th style="width: 12%;">食材名称</th>
                                            <th style="width: 10%;">消耗日期</th>
                                            <th style="width: 8%;">餐次</th>
                                            <th style="width: 18%;">食谱名称</th>
                                            <th style="width: 10%;">食谱分类</th>
                                            <th style="width: 12%;">批次号</th>
                                            <th style="width: 10%;">消耗数量</th>
                                            <th style="width: 12%;">供应商</th>
                                            <th style="width: 8%;">用餐人数</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for trace in ingredient_traceability %}
                                        <tr>
                                            <td>
                                                <div style="font-weight: 600; color: #2c3e50; font-size: 16px;">{{ trace.ingredient_name }}</div>
                                            </td>
                                            <td>
                                                <div style="font-size: 14px;">
                                                    {% if trace.consumption_date %}
                                                        {{ trace.consumption_date.strftime('%m-%d') }}
                                                    {% else %}
                                                        -
                                                    {% endif %}
                                                </div>
                                            </td>
                                            <td>
                                                {% if trace.meal_type %}
                                                    {% if trace.meal_type == '早餐' %}
                                                        <span class="badge badge-info badge-sm">早餐</span>
                                                    {% elif trace.meal_type == '午餐' %}
                                                        <span class="badge badge-success badge-sm">午餐</span>
                                                    {% elif trace.meal_type == '晚餐' %}
                                                        <span class="badge badge-warning badge-sm">晚餐</span>
                                                    {% else %}
                                                        <span class="badge badge-secondary badge-sm">{{ trace.meal_type }}</span>
                                                    {% endif %}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if trace.recipe_name %}
                                                    <div style="font-weight: 500; color: #2c3e50; font-size: 15px;">{{ trace.recipe_name }}</div>
                                                    <small class="text-muted">计划份数: {{ trace.planned_quantity }}份</small>
                                                {% else %}
                                                    <span class="text-muted">未关联食谱</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if trace.recipe_category %}
                                                    <span style="color: #dc3545; font-size: 13px; font-weight: 500;">{{ trace.recipe_category }}</span>
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span style="font-family: 'Courier New', monospace; background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-size: 13px; color: #495057; border: 1px solid #e9ecef;">{{ trace.batch_number }}</span>
                                            </td>
                                            <td class="text-right">
                                                <div style="font-weight: 600; font-size: 16px; color: #2980b9;">{{ trace.consumed_quantity }}{{ trace.unit }}</div>
                                            </td>
                                            <td>
                                                {% if trace.supplier_info %}
                                                    <div style="font-size: 13px; font-weight: 500; color: #2c3e50;">{{ trace.supplier_info.name }}</div>
                                                    {% if trace.supplier_info.contact_person %}
                                                    <small class="text-muted">{{ trace.supplier_info.contact_person }}</small>
                                                    {% endif %}
                                                    {% if trace.supplier_info.phone %}
                                                    <br><small class="text-muted">{{ trace.supplier_info.phone }}</small>
                                                    {% endif %}
                                                {% else %}
                                                    <span class="text-muted">未知供应商</span>
                                                {% endif %}
                                            </td>
                                            <td class="text-center">
                                                {% if trace.diners_count %}
                                                    {{ trace.diners_count }}人
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <div class="mt-3">
                                <small class="text-info">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    溯源原理：通过出库单 → 消耗计划 → 菜单计划 → 食谱配方，实现完整的食品安全追溯链条
                                </small>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- 操作按钮 -->
                {% if stock_out.status == '待审核' or stock_out.status == '已审核' %}
                <div class="card-footer text-center">
                    {% if stock_out.status == '待审核' %}
                    <form action="{{ url_for('stock_out.approve', id=stock_out.id) }}" method="post" style="display: inline;"><button type="submit" class="btn btn-success" {% if not stock_out_items %}disabled{% endif %}>
                            <i class="fas fa-check"></i> 审核通过
                        </button>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                    <form action="{{ url_for('stock_out.cancel', id=stock_out.id) }}" method="post" style="display: inline;"><button type="submit" class="btn btn-danger" data-action="critical-confirm" data-original-data-action="safe-confirm" data-confirm-code="return confirm(" style="cursor: pointer;">
                            <i class="fas fa-times"></i> 取消出库单
                        </button>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" style="cursor: pointer;"></form>
                    {% elif stock_out.status == '已审核' %}
                    <form action="{{ url_for('stock_out.execute', id=stock_out.id) }}" method="post" style="display: inline;"><button type="submit" class="btn btn-success" data-action="critical-confirm" data-original-data-action="safe-confirm" data-confirm-code="return confirm(" style="cursor: pointer;">
                            <i class="fas fa-check-double"></i> 执行出库
                        </button>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" style="cursor: pointer;"></form>
                    <form action="{{ url_for('stock_out.cancel', id=stock_out.id) }}" method="post" style="display: inline;"><button type="submit" class="btn btn-danger" data-action="critical-confirm" data-original-data-action="safe-confirm" data-confirm-code="return confirm(" style="cursor: pointer;">
                            <i class="fas fa-times"></i> 取消出库单
                        </button>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" style="cursor: pointer;"></form>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    function printStockOut(stockOutId) {
        // 创建打印窗口
        let printWindow = window.open('', '_blank', 'height=600,width=800');

        // 构建打印内容
        let printContent = `
            <html>
            <head>
                <title>出库单打印</title>
                <style nonce="{{ csp_nonce }}">
                    body {
                        font-family: Arial, sans-serif;
                        margin: 20px;
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 20px;
                    }
                    .header h2 {
                        margin-bottom: 5px;
                    }
                    .info-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-bottom: 20px;
                    }
                    .info-table th, .info-table td {
                        border: 1px solid #ddd;
                        padding: 8px;
                    }
                    .info-table th {
                        width: 25%;
                        text-align: right;
                        background-color: #f2f2f2;
                    }
                    .items-table {
                        width: 100%;
                        border-collapse: collapse;
                    }
                    .items-table th, .items-table td {
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: center;
                    }
                    .items-table th {
                        background-color: #f2f2f2;
                    }
                    .footer {
                        margin-top: 30px;
                        display: flex;
                        justify-content: space-between;
                    }
                    .signature {
                        width: 45%;
                    }
                    @media print {
                        .no-print {
                            display: none;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <h2>出库单</h2>
                    <p>单号：{{ stock_out.stock_out_number }}</p>
                </div>

                <table class="info-table">
                    <tr>
                        <th>仓库</th>
                        <td>{{ stock_out.warehouse.name }}</td>
                        <th>出库日期</th>
                        <td>{{  stock_out.stock_out_date|format_datetime('%Y-%m-%d %H:%M:%S')  }}</td>
                    </tr>
                    <tr>
                        <th>出库类型</th>
                        <td>{{ stock_out.stock_out_type }}</td>
                        <th>操作人</th>
                        <td>{{ stock_out.operator.real_name or stock_out.operator.username }}</td>
                    </tr>
                    {% if consumption_plan and menu_plan %}
                    <tr>
                        <th>关联消耗计划</th>
                        <td colspan="3">{{ consumption_plan.id }} ({{ menu_plan.plan_date }} {{ menu_plan.meal_type }})</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <th>备注</th>
                        <td colspan="3">{{ stock_out.notes or '-' }}</td>
                    </tr>
                </table>

                <table class="items-table">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>食材名称</th>
                            <th>批次号</th>
                            <th>出库数量</th>
                            <th>单位</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in stock_out_items %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ item.ingredient.name }}</td>
                            <td>{{ item.batch_number }}</td>
                            <td>{{ item.quantity }}</td>
                            <td>{{ item.unit }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>

                <div class="footer">
                    <div class="signature">
                        <p>出库人：{{ stock_out.operator.real_name or stock_out.operator.username }}</p>
                        <p>签名：________________</p>
                    </div>
                    <div class="signature">
                        <p>领用人：________________</p>
                        <p>签名：________________</p>
                    </div>
                </div>

                <div class="no-print" style="text-align: center; margin-top: 20px;">
                    <button class="print-button">打印</button>
                    <button data-data-data-data-data-onclick="window.close()" data-event-id="5585caa5" data-event-id="5585caa5" data-event-id="5585caa5" data-event-id="5585caa5" data-event-id="5585caa5">关闭</button>
                </div>
            
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
</body>
            </html>
        `;

        // 写入打印内容
        printWindow.document.write(printContent);
        printWindow.document.close();

        // 等待页面加载完成后自动打印
        printWindow.onload = function() {
            printWindow.focus();
            // 如果需要自动打印，取消下面这行的注释
            // printWindow.print();
        };
    }
</script>
{% endblock %}
