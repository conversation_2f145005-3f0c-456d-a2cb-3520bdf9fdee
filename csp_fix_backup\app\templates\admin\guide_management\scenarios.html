{% extends "base.html" %}

{% block title %}场景管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-school mr-2"></i>引导场景管理</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('system.index') }}">系统管理</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('guide_management.dashboard') }}">引导管理</a></li>
                    <li class="breadcrumb-item active">场景管理</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-right">
            <button class="btn btn-primary" data-onclick="addNewScenario()" data-event-id="f517506d">
                <i class="fas fa-plus mr-1"></i>添加场景
            </button>
            <a href="{{ url_for('guide_management.dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-1"></i>返回
            </a>
        </div>
    </div>

    <!-- 场景概览 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle mr-2"></i>场景化引导说明</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <p class="mb-2"><strong>场景化引导</strong>根据不同类型的学校提供定制化的引导体验：</p>
                        <ul class="mb-0">
                            <li><strong>小学</strong>：注重简单易用，强调营养搭配和安全管理</li>
                            <li><strong>中学</strong>：平衡功能完整性和操作便捷性</li>
                            <li><strong>高中</strong>：提供完整的食堂管理功能</li>
                            <li><strong>职业学校</strong>：强调实用性和职业技能培养</li>
                            <li><strong>大学</strong>：注重大规模管理和多样化需求</li>
                            <li><strong>乡村学校</strong>：简化流程，适应资源有限的环境</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 场景列表 -->
    <div class="row">
        {% for scenario_key, scenario_info in scenarios.items() %}
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-{{ scenario_info.color }} text-white">
                    <h5 class="mb-0">
                        <i class="{{ scenario_info.icon }} mr-2"></i>
                        {{ scenario_info.name }}
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">{{ scenario_info.description }}</p>

                    <!-- 使用统计 -->
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">使用次数</small>
                            <strong>{{ scenario_stats.get(scenario_key, 0) }}</strong>
                        </div>
                        <div class="progress" style="height: 6px;">
                            {% set usage_percent = (scenario_stats.get(scenario_key, 0) / 100 * 100) if scenario_stats.get(scenario_key, 0) > 0 else 0 %}
                            <div class="progress-bar bg-{{ scenario_info.color }}" style="width: {{ usage_percent }}%"></div>
                        </div>
                    </div>

                    <!-- 特色功能 -->
                    <div class="mb-3">
                        <h6>优先步骤：</h6>
                        {% if scenario_guides.get(scenario_key) %}
                            {% set guide = scenario_guides[scenario_key] %}
                            {% if guide.get('priority_steps') %}
                                <ul class="list-unstyled">
                                    {% for step in guide.priority_steps[:3] %}
                                    <li><i class="fas fa-star text-warning mr-1"></i>
                                        {% if step == 'daily_management' %}日常管理
                                        {% elif step == 'suppliers' %}供应商管理
                                        {% elif step == 'weekly_menu' %}周菜单制定
                                        {% elif step == 'purchase_order' %}采购订单
                                        {% elif step == 'stock_in' %}食材入库
                                        {% elif step == 'food_samples' %}留样记录
                                        {% elif step == 'traceability' %}溯源管理
                                        {% elif step == 'consumption_plan' %}消耗计划
                                        {% else %}{{ step }}
                                        {% endif %}
                                    </li>
                                    {% endfor %}
                                </ul>
                            {% else %}
                                <p class="text-muted">使用默认步骤顺序</p>
                            {% endif %}
                        {% else %}
                            <p class="text-muted">暂无场景配置</p>
                        {% endif %}
                    </div>

                    <!-- 操作按钮 -->
                    <div class="btn-group btn-group-sm w-100">
                        <button class="btn btn-outline-primary" onclick="previewScenario('{{ scenario_key }}')">
                            <i class="fas fa-eye"></i> 预览
                        </button>
                        <button class="btn btn-outline-success" onclick="editScenario('{{ scenario_key }}')">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        <button class="btn btn-outline-info" onclick="testScenario('{{ scenario_key }}')">
                            <i class="fas fa-play"></i> 测试
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- 场景配置统计 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-bar mr-2"></i>场景使用统计</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <canvas id="scenarioChart" width="400" height="200"></canvas>
                        </div>
                        <div class="col-md-4">
                            <h6>统计说明</h6>
                            <div class="list-group list-group-flush">
                                {% for scenario_key, count in scenario_stats.items() %}
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    {{ scenarios[scenario_key].name }}
                                    <span class="badge badge-{{ scenarios[scenario_key].color }}">{{ count }}</span>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 场景预览模态框 -->
<div class="modal fade" id="scenarioPreviewModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">场景预览</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="scenarioPreviewContent">
                <!-- 动态加载内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" data-onclick="applyScenario()" data-event-id="3c9e174e">应用场景</button>
            </div>
        </div>
    </div>
</div>

<!-- 场景编辑模态框 -->
<div class="modal fade" id="scenarioEditModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑场景配置</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="scenarioEditContent">
                <!-- 动态加载编辑表单 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" data-onclick="saveScenario()" data-event-id="a791d7a1">保存配置</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}" src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script nonce="{{ csp_nonce }}">
// 初始化场景使用统计图表
$(document).ready(function() {
    const ctx = document.getElementById('scenarioChart').getContext('2d');
    const scenarioData = {
        labels: [
            {% for scenario_key, scenario_info in scenarios.items() %}
            '{{ scenario_info.name }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: '使用次数',
            data: [
                {% for scenario_key in scenarios.keys() %}
                {{ scenario_stats.get(scenario_key, 0) }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: [
                {% for scenario_key, scenario_info in scenarios.items() %}
                '{% if scenario_info.color == "primary" %}#4e73df{% elif scenario_info.color == "success" %}#1cc88a{% elif scenario_info.color == "info" %}#36b9cc{% elif scenario_info.color == "warning" %}#f6c23e{% elif scenario_info.color == "danger" %}#e74a3b{% else %}#858796{% endif %}'{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            borderWidth: 1
        }]
    };

    new Chart(ctx, {
        type: 'doughnut',
        data: scenarioData,
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});

// 预览场景
function previewScenario(scenarioKey) {
    $('#scenarioPreviewModal').modal('show');
    $('#scenarioPreviewContent').html(`
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p>正在加载场景预览...</p>
        </div>
    `);

    // 模拟加载场景预览内容
    setTimeout(() => {
        const scenarioContent = getScenarioPreviewContent(scenarioKey);
        $('#scenarioPreviewContent').html(scenarioContent);
    }, 1000);
}

// 编辑场景
function editScenario(scenarioKey) {
    $('#scenarioEditModal').modal('show');
    $('#scenarioEditContent').html(`
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p>正在加载场景配置...</p>
        </div>
    `);

    // 模拟加载编辑表单
    setTimeout(() => {
        const editForm = getScenarioEditForm(scenarioKey);
        $('#scenarioEditContent').html(editForm);
    }, 1000);
}

// 测试场景
function testScenario(scenarioKey) {
    if (confirm(`确定要测试 ${scenarioKey} 场景吗？\n\n这将启动一个模拟的引导流程。`)) {
        alert('场景测试功能开发中...');
    }
}

// 添加新场景
function addNewScenario() {
    alert('添加新场景功能开发中...');
}

// 应用场景
function applyScenario() {
    alert('应用场景功能开发中...');
    $('#scenarioPreviewModal').modal('hide');
}

// 保存场景配置
function saveScenario() {
    alert('保存场景配置功能开发中...');
    $('#scenarioEditModal').modal('hide');
}

// 获取场景预览内容
function getScenarioPreviewContent(scenarioKey) {
    return `
        <div class="row">
            <div class="col-md-6">
                <h6>引导步骤</h6>
                <ol class="list-group list-group-numbered">
                    <li class="list-group-item">欢迎介绍</li>
                    <li class="list-group-item">日常管理模块</li>
                    <li class="list-group-item">供应商管理</li>
                    <li class="list-group-item">食材和食谱</li>
                    <li class="list-group-item">周菜单制定</li>
                    <li class="list-group-item">采购订单</li>
                    <li class="list-group-item">食材入库</li>
                    <li class="list-group-item">消耗计划</li>
                    <li class="list-group-item">食材出库</li>
                    <li class="list-group-item">溯源管理</li>
                    <li class="list-group-item">留样记录</li>
                </ol>
            </div>
            <div class="col-md-6">
                <h6>场景特色</h6>
                <div class="alert alert-info">
                    <p>针对 <strong>${scenarioKey}</strong> 类型学校的特色配置：</p>
                    <ul>
                        <li>简化的操作流程</li>
                        <li>重点功能突出</li>
                        <li>适合的演示数据</li>
                        <li>定制化的提示信息</li>
                    </ul>
                </div>

                <h6>预期效果</h6>
                <div class="progress mb-2">
                    <div class="progress-bar bg-success" class="w-85">易用性 85%</div>
                </div>
                <div class="progress mb-2">
                    <div class="progress-bar bg-info" class="w-90">完成率 90%</div>
                </div>
                <div class="progress mb-2">
                    <div class="progress-bar bg-warning" class="w-75">满意度 75%</div>
                </div>
            </div>
        </div>
    `;
}

// 获取场景编辑表单
function getScenarioEditForm(scenarioKey) {
    return `
        <form id="scenarioEditForm">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>场景名称</label>
                        <input type="text" class="form-control" value="${scenarioKey}" readonly>
                    </div>
                    <div class="form-group">
                        <label>显示名称</label>
                        <input type="text" class="form-control" value="场景显示名称">
                    </div>
                    <div class="form-group">
                        <label>描述</label>
                        <textarea class="form-control" rows="3">场景描述信息</textarea>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>主题色彩</label>
                        <select class="form-control">
                            <option value="primary">蓝色</option>
                            <option value="success">绿色</option>
                            <option value="info">青色</option>
                            <option value="warning">黄色</option>
                            <option value="danger">红色</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>图标</label>
                        <input type="text" class="form-control" value="fas fa-school">
                    </div>
                    <div class="form-group">
                        <label>是否启用</label>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" checked>
                            <label class="form-check-label">启用此场景</label>
                        </div>
                    </div>
                </div>
            </div>

            <hr>

            <h6>引导步骤配置</h6>
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>步骤</th>
                            <th>启用</th>
                            <th>标题</th>
                            <th>重要性</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>欢迎介绍</td>
                            <td><input type="checkbox" checked></td>
                            <td><input type="text" class="form-control form-control-sm" value="欢迎使用"></td>
                            <td>
                                <select class="form-control form-control-sm">
                                    <option>高</option>
                                    <option>中</option>
                                    <option>低</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td>日常管理</td>
                            <td><input type="checkbox" checked></td>
                            <td><input type="text" class="form-control form-control-sm" value="日常管理"></td>
                            <td>
                                <select class="form-control form-control-sm">
                                    <option>高</option>
                                    <option>中</option>
                                    <option>低</option>
                                </select>
                            </td>
                        </tr>
                        <!-- 更多步骤... -->
                    </tbody>
                </table>
            </div>
        </form>
    `;
}
</script>
{% endblock %}

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>