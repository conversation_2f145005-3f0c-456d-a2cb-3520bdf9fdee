{% extends "base.html" %}

{% block title %}咨询详情 - {{ consultation.name }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">咨询详情 #{{ consultation.id }}</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('consultation.consultation_list') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <!-- 咨询信息 -->
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">咨询信息</h4>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th width="150">咨询ID</th>
                                            <td>{{ consultation.id }}</td>
                                        </tr>
                                        <tr>
                                            <th>姓名</th>
                                            <td>{{ consultation.name }}</td>
                                        </tr>
                                        <tr>
                                            <th>联系方式</th>
                                            <td>
                                                <span class="badge badge-info">{{ consultation.contact_type }}</span>
                                                {{ consultation.contact_value }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>状态</th>
                                            <td>
                                                {% if consultation.status == '待处理' %}
                                                    <span class="badge badge-warning">{{ consultation.status }}</span>
                                                {% elif consultation.status == '已回复' %}
                                                    <span class="badge badge-success">{{ consultation.status }}</span>
                                                {% else %}
                                                    <span class="badge badge-secondary">{{ consultation.status }}</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>来源</th>
                                            <td>{{ consultation.source }}</td>
                                        </tr>
                                        <tr>
                                            <th>提交时间</th>
                                            <td>{{ consultation.created_at|format_datetime }}</td>
                                        </tr>
                                        <tr>
                                            <th>IP地址</th>
                                            <td>{{ consultation.ip_address or '未记录' }}</td>
                                        </tr>
                                    </table>
                                    
                                    <div class="mt-3">
                                        <h5>咨询内容</h5>
                                        <div class="border p-3 bg-light">
                                            {{ consultation.content|nl2br|safe }}
                                        </div>
                                    </div>
                                    
                                    {% if consultation.reply_content %}
                                    <div class="mt-3">
                                        <h5>回复内容</h5>
                                        <div class="border p-3 bg-info text-white">
                                            {{ consultation.reply_content|nl2br|safe }}
                                        </div>
                                        <small class="text-muted">
                                            回复时间：{{ consultation.reply_time|format_datetime }}
                                            {% if consultation.reply_user %}
                                                | 回复人：{{ consultation.reply_user.real_name }}
                                            {% endif %}
                                        </small>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- 操作面板 -->
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">操作</h4>
                                </div>
                                <div class="card-body">
                                    {% if consultation.status == '待处理' or consultation.status == '已回复' %}
                                    <!-- 回复表单 -->
                                    <form method="POST" action="{{ url_for('consultation.reply_consultation', id=consultation.id) }}">
                                        {{ reply_form.hidden_tag() }}
                                        <div class="form-group">
                                            {{ reply_form.reply_content.label(class="form-label") }}
                                            {{ reply_form.reply_content(class="form-control", rows="6", placeholder="请输入回复内容...") }}
                                        </div>
                                        <div class="form-group">
                                            {{ reply_form.submit(class="btn btn-primary btn-block") }}
                                        </div>
                                    </form>
                                    
                                    <hr>
                                    
                                    <!-- 关闭咨询 -->
                                    <form method="POST" action="{{ url_for('consultation.close_consultation', id=consultation.id) }}" 
                                          data-validation="critical" data-original-data-data-data-data-onsubmit="return confirm(" data-event-id="c5e437ca" data-event-id="c5e437ca" data-event-id="c5e437ca" data-event-id="c5e437ca"确定要关闭这个咨询吗？')">
                                        <button type="submit" class="btn btn-secondary btn-block">
                                            <i class="fas fa-times"></i> 关闭咨询
                                        </button>
                                    </form>
                                    {% else %}
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        该咨询已关闭，无法进行操作。
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- 技术信息 -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h4 class="card-title">技术信息</h4>
                                </div>
                                <div class="card-body">
                                    <small class="text-muted">
                                        <strong>User Agent:</strong><br>
                                        {{ consultation.user_agent or '未记录' }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}
