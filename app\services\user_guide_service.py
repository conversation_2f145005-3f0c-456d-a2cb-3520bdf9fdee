#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户引导服务
为新用户提供分步骤的系统使用引导
"""

from flask import session, url_for
from datetime import datetime, date
import json
from .video_guide_service import VideoGuideService
from .scenario_guide_service import ScenarioGuideService

class UserGuideService:
    """用户引导服务类"""

    # 引导步骤定义
    GUIDE_STEPS = {
        'welcome': {
            'title': '欢迎使用校园餐智慧食堂平台',
            'description': '让我们一起了解食堂管理的完整流程',
            'order': 0
        },
        'daily_management': {
            'title': '食堂日常管理模块',
            'description': '了解6个日常管理子功能，体验二维码检查和PDF打印',
            'order': 1
        },
        'suppliers': {
            'title': '供应商管理',
            'description': '建立供应商档案，为采购做准备',
            'order': 2
        },
        'ingredients_recipes': {
            'title': '食材食谱管理',
            'description': '添加食材和食谱，建立菜品数据库',
            'order': 3
        },
        'weekly_menu': {
            'title': '周菜单计划',
            'description': '制定周菜单，安排每日餐食',
            'order': 4
        },
        'purchase_order': {
            'title': '采购订单',
            'description': '从周菜单生成采购单，规范采购流程',
            'order': 5
        },
        'stock_in': {
            'title': '食材入库',
            'description': '记录入库信息，建立溯源档案',
            'order': 6
        },
        'consumption_plan': {
            'title': '消耗量计划',
            'description': '制定食材使用计划，精确控制用量',
            'order': 7
        },
        'stock_out': {
            'title': '食材出库',
            'description': '记录出库信息，完善溯源链条',
            'order': 8
        },
        'traceability': {
            'title': '食材溯源',
            'description': '实现从采购到餐桌的全程追溯',
            'order': 9
        },
        'food_samples': {
            'title': '留样记录',
            'description': '一键生成留样记录，确保食品安全',
            'order': 10
        },
        'completed': {
            'title': '引导完成',
            'description': '恭喜您已掌握完整的食堂管理流程',
            'order': 11
        }
    }

    @staticmethod
    def get_user_guide_status(user_id):
        """获取用户引导状态"""
        guide_key = f'user_guide_{user_id}'
        guide_data = session.get(guide_key, {})

        return {
            'current_step': guide_data.get('current_step', 'welcome'),
            'completed_steps': guide_data.get('completed_steps', []),
            'started_at': guide_data.get('started_at'),
            'is_active': guide_data.get('is_active', True)
        }

    @staticmethod
    def start_guide(user_id, school_type=None, user_preferences=None):
        """开始用户引导"""
        guide_key = f'user_guide_{user_id}'

        # 创建个性化引导计划
        if school_type:
            guide_plan = ScenarioGuideService.create_personalized_guide_plan(school_type, user_preferences)
        else:
            guide_plan = None

        session[guide_key] = {
            'current_step': 'welcome',
            'completed_steps': [],
            'started_at': datetime.now().isoformat(),
            'is_active': True,
            'school_type': school_type,
            'guide_plan': guide_plan,
            'user_preferences': user_preferences or {}
        }
        return True

    @staticmethod
    def complete_step(user_id, step_name):
        """完成一个引导步骤"""
        guide_key = f'user_guide_{user_id}'
        guide_data = session.get(guide_key, {})

        if step_name not in guide_data.get('completed_steps', []):
            guide_data.setdefault('completed_steps', []).append(step_name)

        # 获取下一步
        next_step = UserGuideService._get_next_step(step_name)
        guide_data['current_step'] = next_step

        session[guide_key] = guide_data
        return next_step

    @staticmethod
    def _get_next_step(current_step):
        """获取下一个步骤"""
        current_order = UserGuideService.GUIDE_STEPS.get(current_step, {}).get('order', 0)

        for step_name, step_info in UserGuideService.GUIDE_STEPS.items():
            if step_info['order'] == current_order + 1:
                return step_name

        return 'completed'

    @staticmethod
    def skip_guide(user_id):
        """跳过引导"""
        guide_key = f'user_guide_{user_id}'
        guide_data = session.get(guide_key, {})
        guide_data['is_active'] = False
        session[guide_key] = guide_data
        return True

    @staticmethod
    def get_step_info(step_name):
        """获取步骤信息"""
        return UserGuideService.GUIDE_STEPS.get(step_name, {})

    @staticmethod
    def get_step_content(step_name, user_id=None):
        """获取步骤详细内容"""
        # 获取用户的引导状态
        guide_status = UserGuideService.get_user_guide_status(user_id) if user_id else {}
        school_type = guide_status.get('school_type')

        # 获取场景化定制内容
        customized_content = {}
        if school_type:
            customized_content = ScenarioGuideService.get_customized_step_content(school_type, step_name)

        # 获取视频资源
        video_resources = VideoGuideService.get_video_resources(step_name)

        content_map = {
            'welcome': {
                'features': [
                    '完整的食堂管理工作流程',
                    '从采购到餐桌的全程追溯',
                    '专业的PDF报表生成',
                    '移动端二维码检查',
                    '智能化数据分析'
                ],
                'next_action': '开始了解日常管理',
                'next_url': 'daily_management.index'
            },
            'daily_management': {
                'modules': [
                    {'name': '检查记录', 'icon': 'fa-search', 'desc': '食品安全检查，支持二维码扫描上传'},
                    {'name': '陪餐记录', 'icon': 'fa-users', 'desc': '陪餐人员记录，生成陪餐报告'},
                    {'name': '培训记录', 'icon': 'fa-graduation-cap', 'desc': '员工培训档案管理'},
                    {'name': '特殊事件', 'icon': 'fa-exclamation-triangle', 'desc': '突发事件记录处理'},
                    {'name': '问题记录', 'icon': 'fa-bug', 'desc': '问题发现与整改跟踪'},
                    {'name': '工作日志', 'icon': 'fa-calendar-check', 'desc': '日常工作记录，生成工作报告'}
                ],
                'highlights': [
                    '生成学校专属二维码，员工扫码上传检查数据',
                    '一键生成PDF报告，方便部门检查',
                    '整合相关资料，形成完整档案'
                ],
                'next_action': '了解供应商管理',
                'next_url': 'supplier.index'
            },
            'suppliers': {
                'importance': [
                    '建立合格供应商档案',
                    '确保食材来源可靠',
                    '规范采购流程',
                    '建立供应商评价体系'
                ],
                'demo_data': {
                    'name': '绿色农场有限公司',
                    'contact': '张经理',
                    'phone': '***********',
                    'products': ['新鲜蔬菜', '有机水果', '绿色大米']
                },
                'next_action': '添加食材和食谱',
                'next_url': 'ingredient.index'
            }
            # 其他步骤内容...
        }

        return content_map.get(step_name, {})

    @staticmethod
    def generate_demo_data(step_name, user_area_id):
        """为指定步骤生成演示数据"""
        if step_name == 'suppliers':
            return UserGuideService._create_demo_supplier(user_area_id)
        elif step_name == 'ingredients_recipes':
            return UserGuideService._create_demo_ingredients(user_area_id)
        # 其他演示数据生成...

        return None

    @staticmethod
    def _create_demo_supplier(area_id):
        """创建演示供应商数据"""
        from app.models import Supplier, SupplierCategory
        from app import db
        from sqlalchemy import text
        from datetime import datetime, date

        try:
            # 检查是否已存在演示供应商分类
            existing_category = db.session.execute(text('''
                SELECT id FROM supplier_categories WHERE name = :name
            '''), {'name': '蔬菜供应商'}).scalar()

            if existing_category:
                category_id = existing_category
            else:
                # 创建供应商分类
                sql = text('''
                INSERT INTO supplier_categories (name, description, created_at)
                OUTPUT inserted.id
                VALUES (:name, :description, GETDATE())
                ''')

                result = db.session.execute(sql, {
                    'name': '蔬菜供应商',
                    'description': '提供新鲜蔬菜的供应商'
                })
                category_id = result.scalar()

            # 检查是否已存在演示供应商
            existing_supplier = db.session.execute(text('''
                SELECT id FROM suppliers WHERE name = :name
            '''), {'name': '绿色农场有限公司'}).scalar()

            if existing_supplier:
                supplier_id = existing_supplier
            else:
                # 创建演示供应商
                sql = text('''
                INSERT INTO suppliers
                (name, contact_person, phone, email, address, business_license,
                 status, category_id, created_at, updated_at)
                OUTPUT inserted.id
                VALUES
                (:name, :contact_person, :phone, :email, :address, :business_license,
                 :status, :category_id, GETDATE(), GETDATE())
                ''')

                result = db.session.execute(sql, {
                    'name': '绿色农场有限公司',
                    'contact_person': '张经理',
                    'phone': '***********',
                    'email': '<EMAIL>',
                    'address': '北京市昌平区绿色农业园区',
                    'business_license': '91110000123456789X',
                    'status': 1,
                    'category_id': category_id
                })

                supplier_id = result.scalar()

            # 检查是否已存在供应商-学校关联关系
            existing_relation = db.session.execute(text('''
                SELECT id FROM supplier_school_relations
                WHERE supplier_id = :supplier_id AND area_id = :area_id
            '''), {'supplier_id': supplier_id, 'area_id': area_id}).scalar()

            if not existing_relation:
                # 创建供应商-学校关联关系
                # 使用字符串格式化避免date类型参数绑定问题
                contract_number = f"DEMO-{datetime.now().strftime('%Y%m%d')}-{supplier_id:04d}"
                start_date_str = date.today().strftime('%Y-%m-%d')

                relation_sql = text(f'''
                INSERT INTO supplier_school_relations
                (supplier_id, area_id, contract_number, start_date, status, created_at, updated_at)
                OUTPUT inserted.id
                VALUES
                ({supplier_id}, {area_id}, '{contract_number}', '{start_date_str}', 1, GETDATE(), GETDATE())
                ''')

                relation_result = db.session.execute(relation_sql)
                relation_id = relation_result.scalar()
            else:
                relation_id = existing_relation

            db.session.commit()

            return {
                'supplier_id': supplier_id,
                'category_id': category_id,
                'relation_id': relation_id,
                'message': '演示供应商创建成功'
            }

        except Exception as e:
            db.session.rollback()
            return {'error': str(e)}

    @staticmethod
    def _create_demo_ingredients(area_id):
        """创建演示食材数据"""
        # 实现演示食材创建逻辑
        pass
