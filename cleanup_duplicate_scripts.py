#!/usr/bin/env python3
"""
清理重复脚本引用工具
清理模板中重复的universal-event-handler.js引用
"""

import os
import re
from pathlib import Path

def cleanup_duplicate_scripts():
    """清理重复的脚本引用"""
    
    print("🧹 清理重复脚本引用工具")
    print("=" * 40)
    
    template_dir = Path("app/templates")
    if not template_dir.exists():
        print("❌ 模板目录不存在")
        return
    
    fixed_files = []
    
    for html_file in template_dir.rglob("*.html"):
        try:
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 查找universal-event-handler.js引用
            pattern = r'<script[^>]*src="[^"]*universal-event-handler\.js"[^>]*></script>'
            matches = re.findall(pattern, content)
            
            if len(matches) > 1:
                # 保留最后一个引用，删除其他的
                lines = content.split('\n')
                script_lines = []
                
                for i, line in enumerate(lines):
                    if 'universal-event-handler.js' in line:
                        script_lines.append(i)
                
                if len(script_lines) > 1:
                    # 删除除最后一个之外的所有引用
                    for line_num in script_lines[:-1]:
                        lines[line_num] = ''
                    
                    # 重新组合内容，移除空行
                    new_lines = []
                    for line in lines:
                        if line.strip() or (new_lines and new_lines[-1].strip()):
                            new_lines.append(line)
                    
                    content = '\n'.join(new_lines)
            
            # 如果内容有变化，写回文件
            if content != original_content:
                with open(html_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                rel_path = html_file.relative_to(Path("."))
                fixed_files.append(rel_path)
                print(f"✅ 清理文件: {rel_path} (移除了 {len(matches)-1} 个重复引用)")
                
        except Exception as e:
            print(f"❌ 处理文件 {html_file} 时出错: {str(e)}")
    
    print(f"\n🎉 清理完成!")
    print(f"✅ 清理了 {len(fixed_files)} 个文件")
    
    if fixed_files:
        print("\n清理的文件:")
        for file_path in fixed_files:
            print(f"  - {file_path}")

def main():
    """主函数"""
    cleanup_duplicate_scripts()

if __name__ == "__main__":
    main()
