/**
 * 增强的关键事件处理器
 * 处理所有类型的删除确认和表单验证
 */


    // 安全执行函数（替代eval）
    function safeExecute(code) {
        if (!code) return;
        code = code.trim();
        
        // 处理confirm调用
        if (code.includes('confirm(')) {
            const confirmMatch = code.match(/confirm\s*\(\s*['"](.*?)['"]\s*\)/);
            if (confirmMatch) {
                return confirm(confirmMatch[1]);
            }
        }
        
        console.warn('不支持的代码执行:', code);
        return false;
    }
    
    // 安全函数创建器（替代Function构造器）
    function createSafeFunction(code) {
        return function() {
            return safeExecute(code);
        };
    }

    document.addEventListener('DOMContentLoaded', function() {

    // 处理所有关键确认操作
    document.querySelectorAll('[data-action="critical-confirm"]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();

            const originalOnclick = this.getAttribute('data-original-onclick');

            // 检查 originalOnclick 是否存在且包含 confirm
            if (originalOnclick && originalOnclick.includes('confirm(')) {
                // 提取确认消息
                const confirmMatch = originalOnclick.match(/confirm\s*\(\s*['"](.*?)['"]\s*\)/);
                const confirmMessage = confirmMatch ? confirmMatch[1] : '确定要执行此操作吗？';

                if (confirm(confirmMessage)) {
                    try {
                        // 执行原有逻辑
                        executeOriginalCode(originalOnclick);
                    } catch (error) {
                        console.error('执行失败:', error);
                        alert('操作失败，请重试');
                    }
                }
            } else if (originalOnclick) {
                // 对于没有确认的删除操作，添加确认
                if (originalOnclick.toLowerCase().includes('delete') ||
                    originalOnclick.toLowerCase().includes('remove')) {

                    if (confirm('确定要删除吗？')) {
                        try {
                            executeOriginalCode(originalOnclick);
                        } catch (error) {
                            console.error('删除失败:', error);
                            alert('删除失败，请重试');
                        }
                    }
                } else {
                    // 其他操作直接执行
                    try {
                        executeOriginalCode(originalOnclick);
                    } catch (error) {
                        console.error('执行失败:', error);
                    }
                }
            } else {
                // originalOnclick 为空的情况，记录警告
                console.warn('按钮缺少 data-original-onclick 属性:', this);
            }
        });
    });

    // 处理关键表单验证
    document.querySelectorAll('[data-validation="critical"]').forEach(function(form) {
        form.addEventListener('submit', function(e) {
            const originalOnsubmit = this.getAttribute('data-original-onsubmit');

            try {
                // 执行原有验证逻辑
                const result = executeOriginalCode(originalOnsubmit);

                // 如果返回 false，阻止提交
                if (result === false) {
                    e.preventDefault();
                    console.log('表单验证失败，已阻止提交');
                }
            } catch (error) {
                console.error('表单验证失败:', error);
                e.preventDefault();
                alert('表单验证失败，请检查输入');
            }
        });
    });

    // 处理之前的删除确认（兼容性）
    document.querySelectorAll('[data-action="delete-confirm"]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();

            const functionCode = this.getAttribute('data-function');
            const confirmMessage = '确定要执行此操作吗？';

            if (confirm(confirmMessage)) {
                try {
                    executeOriginalCode(functionCode);
                } catch (error) {
                    console.error('执行失败:', error);
                    alert('操作失败，请重试');
                }
            }
        });
    });

    // 处理之前的表单验证（兼容性）
    document.querySelectorAll('[data-validation="true"]').forEach(function(form) {
        form.addEventListener('submit', function(e) {
            const validator = this.getAttribute('data-validator');

            try {
                const result = executeOriginalCode(validator);
                if (result === false) {
                    e.preventDefault();
                    console.log('表单验证失败');
                }
            } catch (error) {
                console.error('验证失败:', error);
                e.preventDefault();
                alert('表单验证失败，请检查输入');
            }
        });
    });

    // 安全执行原有代码的函数
    function executeOriginalCode(code) {
        if (!code) return;

        try {
            // 清理代码
            code = code.trim();

            // 如果是 return 语句，提取返回值
            if (code.startsWith('return ')) {
                code = code.substring(7);
                return safeExecute(code);
            } else {
                // 直接执行
                safeExecute(code);
                return true;
            }
        } catch (error) {
            console.error('代码执行失败:', error);
            throw error;
        }
    }

    console.log('✅ 增强的关键事件处理器已加载');
});