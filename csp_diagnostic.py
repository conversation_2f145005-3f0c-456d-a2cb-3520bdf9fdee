#!/usr/bin/env python3
"""
CSP问题诊断工具
检查和诊断CSP相关的配置和问题
"""

import os
import re
import requests
from pathlib import Path

class CSPDiagnostic:
    def __init__(self, project_root=".", app_url="http://127.0.0.1:5000"):
        self.project_root = Path(project_root)
        self.app_url = app_url
        self.issues = []
        self.recommendations = []
    
    def check_csp_config(self):
        """检查CSP配置"""
        print("🔍 检查CSP配置...")
        
        init_file = self.project_root / "app" / "__init__.py"
        if not init_file.exists():
            self.issues.append("找不到 app/__init__.py 文件")
            return
        
        try:
            with open(init_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查CSP配置
            if "Content-Security-Policy" in content:
                print("✅ 找到CSP配置")
                
                # 检查script-src配置
                if "'unsafe-inline'" in content:
                    print("✅ CSP允许内联脚本 ('unsafe-inline')")
                else:
                    self.issues.append("CSP不允许内联脚本，缺少 'unsafe-inline'")
                
                # 检查nonce配置
                if "nonce-{nonce}" in content:
                    print("✅ CSP配置了nonce支持")
                else:
                    self.issues.append("CSP未配置nonce支持")
                
                # 提取CSP策略
                csp_match = re.search(r'csp_policy = \(\s*"([^"]+)"', content, re.MULTILINE | re.DOTALL)
                if csp_match:
                    csp_policy = csp_match.group(1)
                    print(f"📋 当前CSP策略: {csp_policy[:100]}...")
                
            else:
                self.issues.append("未找到CSP配置")
                
        except Exception as e:
            self.issues.append(f"读取CSP配置失败: {str(e)}")
    
    def check_universal_handler(self):
        """检查通用事件处理器"""
        print("\n🔍 检查通用事件处理器...")
        
        handler_file = self.project_root / "app" / "static" / "js" / "universal-event-handler.js"
        if handler_file.exists():
            print("✅ 通用事件处理器文件存在")
            
            try:
                with open(handler_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查关键功能
                if "data-onclick" in content:
                    print("✅ 支持 data-onclick 事件")
                else:
                    self.issues.append("通用事件处理器不支持 data-onclick")
                
                if "DOMContentLoaded" in content:
                    print("✅ 正确绑定到 DOMContentLoaded 事件")
                else:
                    self.issues.append("通用事件处理器未正确绑定到 DOMContentLoaded")
                    
            except Exception as e:
                self.issues.append(f"读取通用事件处理器失败: {str(e)}")
        else:
            self.issues.append("通用事件处理器文件不存在")
            self.recommendations.append("创建 app/static/js/universal-event-handler.js 文件")
    
    def check_inline_handlers(self):
        """检查模板中的内联事件处理器"""
        print("\n🔍 检查模板中的内联事件处理器...")
        
        template_dir = self.project_root / "app" / "templates"
        if not template_dir.exists():
            self.issues.append("模板目录不存在")
            return
        
        inline_patterns = [
            r'onclick\s*=\s*["\'][^"\']+["\']',
            r'onchange\s*=\s*["\'][^"\']+["\']',
            r'onsubmit\s*=\s*["\'][^"\']+["\']',
            r'onload\s*=\s*["\'][^"\']+["\']',
        ]
        
        inline_count = 0
        files_with_inline = []
        
        for html_file in template_dir.rglob("*.html"):
            try:
                with open(html_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                file_inline_count = 0
                for pattern in inline_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    file_inline_count += len(matches)
                
                if file_inline_count > 0:
                    inline_count += file_inline_count
                    rel_path = html_file.relative_to(self.project_root)
                    files_with_inline.append((rel_path, file_inline_count))
                    
            except Exception as e:
                self.issues.append(f"读取模板文件 {html_file} 失败: {str(e)}")
        
        if inline_count > 0:
            print(f"⚠️  找到 {inline_count} 个内联事件处理器")
            print("   包含内联处理器的文件:")
            for file_path, count in files_with_inline[:5]:  # 只显示前5个
                print(f"     - {file_path}: {count} 个")
            if len(files_with_inline) > 5:
                print(f"     ... 还有 {len(files_with_inline) - 5} 个文件")
            
            self.recommendations.append("考虑将内联事件处理器转换为 data-onclick 属性")
        else:
            print("✅ 未找到内联事件处理器")
    
    def check_app_response(self):
        """检查应用程序响应头"""
        print("\n🔍 检查应用程序响应头...")
        
        try:
            response = requests.head(self.app_url, timeout=5)
            
            # 检查CSP头
            csp_header = response.headers.get('Content-Security-Policy')
            if csp_header:
                print("✅ 应用程序返回CSP头")
                print(f"📋 CSP头内容: {csp_header[:100]}...")
                
                if "'unsafe-inline'" in csp_header:
                    print("✅ CSP头允许内联脚本")
                else:
                    self.issues.append("CSP头不允许内联脚本")
                    
            else:
                self.issues.append("应用程序未返回CSP头")
            
            # 检查其他安全头
            security_headers = [
                'X-Content-Type-Options',
                'X-Frame-Options',
                'X-XSS-Protection'
            ]
            
            for header in security_headers:
                if header in response.headers:
                    print(f"✅ {header}: {response.headers[header]}")
                else:
                    print(f"⚠️  缺少 {header} 头")
                    
        except requests.exceptions.RequestException as e:
            self.issues.append(f"无法连接到应用程序: {str(e)}")
            self.recommendations.append("确保应用程序正在运行")
    
    def generate_fix_script(self):
        """生成修复脚本"""
        if not self.issues:
            return
        
        print("\n🔧 生成修复建议...")
        
        fix_script = []
        
        # CSP配置修复
        if any("CSP不允许内联脚本" in issue for issue in self.issues):
            fix_script.append("""
# 修复CSP配置，允许内联脚本
# 在 app/__init__.py 中找到 script-src 配置行，添加 'unsafe-inline'
# 例如：
# script-src 'self' 'nonce-{nonce}' 'unsafe-inline' 'unsafe-hashes' https: http:;
""")
        
        # 通用事件处理器修复
        if any("通用事件处理器" in issue for issue in self.issues):
            fix_script.append("""
# 创建通用事件处理器
# 运行: python quick_csp_fix.py
""")
        
        if fix_script:
            print("💡 修复建议:")
            for script in fix_script:
                print(script.strip())
    
    def run(self):
        """运行诊断"""
        print("🩺 CSP问题诊断工具")
        print("=" * 50)
        
        # 执行各项检查
        self.check_csp_config()
        self.check_universal_handler()
        self.check_inline_handlers()
        self.check_app_response()
        
        # 输出结果
        print("\n" + "=" * 50)
        print("📊 诊断结果")
        
        if not self.issues:
            print("🎉 未发现问题！CSP配置看起来正常。")
        else:
            print(f"❌ 发现 {len(self.issues)} 个问题:")
            for i, issue in enumerate(self.issues, 1):
                print(f"  {i}. {issue}")
        
        if self.recommendations:
            print(f"\n💡 建议 ({len(self.recommendations)} 项):")
            for i, rec in enumerate(self.recommendations, 1):
                print(f"  {i}. {rec}")
        
        # 生成修复脚本
        self.generate_fix_script()
        
        print(f"\n🔗 有用的链接:")
        print(f"  - MDN CSP文档: https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP")
        print(f"  - CSP评估工具: https://csp-evaluator.withgoogle.com/")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='CSP问题诊断工具')
    parser.add_argument('--project-root', default='.', help='项目根目录路径')
    parser.add_argument('--app-url', default='http://127.0.0.1:5000', help='应用程序URL')
    
    args = parser.parse_args()
    
    diagnostic = CSPDiagnostic(args.project_root, args.app_url)
    diagnostic.run()

if __name__ == "__main__":
    main()
