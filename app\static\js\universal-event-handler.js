/**
 * 通用事件处理器 - CSP安全版本
 * 处理从内联事件迁移过来的事件处理，不使用eval()或Function构造器
 */

(function() {
    'use strict';
    
    // 预定义的安全函数映射
    const safeFunctions = {
        // 窗口操作
        'window.print()': () => window.print(),
        'window.close()': () => window.close(),
        'window.history.back()': () => window.history.back(),
        'history.back()': () => history.back(),
        'location.reload()': () => location.reload(),
        
        // 确认对话框
        'confirm': (message) => confirm(message),
        
        // 表单操作
        'resetForm()': () => {
            if (typeof resetForm === 'function') resetForm();
        },
        
        // 通用函数调用
        'goToDate': (event) => {
            if (typeof goToDate === 'function') goToDate(event);
        }
    };
    
    // 安全执行函数
    function safeExecute(funcCall) {
        // 清理函数调用
        funcCall = funcCall.trim();
        
        // 处理简单的函数调用
        if (safeFunctions[funcCall]) {
            return safeFunctions[funcCall]();
        }
        
        // 处理带参数的函数调用
        const funcMatch = funcCall.match(/^(\w+)\((.*)\)$/);
        if (funcMatch) {
            const funcName = funcMatch[1];
            const args = funcMatch[2];
            
            if (funcName === 'confirm' && args) {
                // 处理confirm调用
                const message = args.replace(/['"]/g, '');
                return confirm(message);
            }
            
            // 检查全局函数是否存在
            if (typeof window[funcName] === 'function') {
                try {
                    // 简单参数解析（仅支持字符串和数字）
                    const parsedArgs = args.split(',').map(arg => {
                        arg = arg.trim();
                        if (arg.startsWith('"') || arg.startsWith("'")) {
                            return arg.slice(1, -1);
                        }
                        if (!isNaN(arg)) {
                            return Number(arg);
                        }
                        return arg;
                    });
                    return window[funcName](...parsedArgs);
                } catch (e) {
                    console.warn('函数调用失败:', funcName, e);
                }
            }
        }
        
        // 处理return语句
        if (funcCall.startsWith('return ')) {
            const returnValue = funcCall.substring(7);
            return safeExecute(returnValue);
        }
        
        console.warn('不支持的函数调用:', funcCall);
        return false;
    }
    
    // 等待 DOM 加载完成
    document.addEventListener('DOMContentLoaded', function() {
        
        // 处理通用 onclick 事件
        document.querySelectorAll('[data-onclick]').forEach(function(element) {
            const funcCall = element.getAttribute('data-onclick');
            element.addEventListener('click', function(e) {
                e.preventDefault();
                try {
                    safeExecute(funcCall);
                } catch (error) {
                    console.error('事件处理器执行失败:', error, '函数:', funcCall);
                }
            });
        });
        
        // 处理表单提交事件
        document.querySelectorAll('[data-onsubmit]').forEach(function(form) {
            const funcCall = form.getAttribute('data-onsubmit');
            form.addEventListener('submit', function(e) {
                try {
                    const result = safeExecute(funcCall);
                    if (result === false) {
                        e.preventDefault();
                    }
                } catch (error) {
                    console.error('表单提交处理器执行失败:', error);
                    e.preventDefault();
                }
            });
        });
        
        // 处理其他事件类型
        const eventTypes = ['change', 'focus', 'blur', 'mouseover', 'mouseout', 'load'];
        eventTypes.forEach(function(eventType) {
            const selector = '[data-on' + eventType + ']';
            document.querySelectorAll(selector).forEach(function(element) {
                const funcCall = element.getAttribute('data-on' + eventType);
                element.addEventListener(eventType, function(e) {
                    try {
                        safeExecute(funcCall);
                    } catch (error) {
                        console.error('事件处理器执行失败:', error, '函数:', funcCall);
                    }
                });
            });
        });
        
        console.log('✅ 通用事件处理器已加载 (CSP安全版本)');
    });
})();