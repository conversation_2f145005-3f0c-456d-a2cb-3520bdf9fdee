{% extends 'base.html' %}

{% block title %}检查记录 - {{ log.log_date.strftime('%Y-%m-%d') }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    /* 日期导航样式 */
    .date-navigation {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1.5rem;
        background-color: #f8f9fc;
        border-radius: 0.35rem;
        padding: 0.75rem 1.25rem;
        box-shadow: 0 0.15rem 0.5rem rgba(58, 59, 69, 0.15);
    }

    .date-navigation .date-display {
        font-size: 1.25rem;
        font-weight: 700;
        color: #4e73df;
        margin: 0;
    }

    .date-navigation .nav-buttons {
        display: flex;
        gap: 0.5rem;
    }

    /* 检查类别卡片样式 */
    .category-cards {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .category-card {
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        transition: all 0.3s ease;
        background-color: #fff;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .category-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2);
    }

    .category-card-header {
        background-color: #4e73df;
        color: white;
        padding: 0.75rem 1.25rem;
        font-weight: 700;
        font-size: 1.1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .category-card-body {
        padding: 1.25rem;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
    }

    .category-card-photo {
        position: relative;
        width: 100%;
        padding-top: 56.25%; /* 16:9 Aspect Ratio */
        margin-bottom: 1rem;
        background-color: #f8f9fc;
        border-radius: 0.35rem;
        overflow: hidden;
    }

    .category-card-photo img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        cursor: pointer;
        transition: transform 0.3s ease;
    }

    .category-card-photo img:hover {
        transform: scale(1.05);
    }

    .category-card-photo .no-photo {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #b7b9cc;
        font-size: 0.9rem;
    }

    /* 时段状态样式 */
    .inspection-periods {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .period-status {
        flex: 1;
        border-radius: 0.35rem;
        padding: 0.75rem;
        text-align: center;
        background-color: #f8f9fc;
        transition: all 0.2s ease;
        cursor: pointer;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
    }

    .period-status:hover {
        background-color: #eaecf4;
    }

    .period-status .period-icon {
        font-size: 1.25rem;
        margin-bottom: 0.25rem;
    }

    .period-status .period-name {
        font-weight: 600;
        font-size: 0.85rem;
        margin-bottom: 0.25rem;
    }

    .period-status .period-rating {
        color: #f6c23e;
        font-size: 0.85rem;
        margin-bottom: 0.25rem;
    }

    .period-status .period-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .period-status .period-badge.status-normal {
        background-color: #1cc88a;
        color: white;
    }

    .period-status .period-badge.status-unchecked {
        background-color: #e3e6f0;
        color: #858796;
    }

    .period-status .period-thumbnail {
        width: 40px;
        height: 40px;
        border-radius: 0.25rem;
        object-fit: cover;
        border: 1px solid #e3e6f0;
    }

    /* 描述样式 */
    .category-card-description {
        margin-top: auto;
        padding-top: 0.75rem;
        border-top: 1px solid #e3e6f0;
        font-size: 0.85rem;
        color: #6e707e;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .date-navigation {
            flex-direction: column;
            gap: 0.75rem;
            padding: 0.75rem;
        }

        .category-cards {
            grid-template-columns: 1fr;
        }
    }
</style>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 日期导航 -->
    <div class="date-navigation">
        <h1 class="date-display">检查记录 - {{ log.log_date.strftime('%Y-%m-%d') }}</h1>
        <div class="nav-buttons">
            {% if prev_log %}
            <a href="{{ url_for('daily_management.inspections_category_cards', log_id=prev_log.id) }}" class="btn btn-primary btn-sm">
                <i class="fas fa-chevron-left mr-1"></i> 前一天
            </a>
            {% else %}
            <button class="btn btn-primary btn-sm disabled" disabled>
                <i class="fas fa-chevron-left mr-1"></i> 前一天
            </button>
            {% endif %}

            <div class="btn-group">
                <a href="{{ url_for('daily_management.edit_log', date_str=log.log_date.strftime('%Y-%m-%d')) }}" class="btn btn-info btn-sm">
                    <i class="fas fa-calendar-day mr-1"></i> 日志详情
                </a>
                <button type="button" class="btn btn-info btn-sm dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span class="sr-only">切换下拉菜单</span>
                </button>
                <div class="dropdown-menu dropdown-menu-right">
                    <form class="px-3 py-2" data-onsubmit="goToDate(event)" data-event-id="1f21e4b2">
                        <div class="form-group mb-2">
                            <label for="dateInput">选择日期</label>
                            <input type="date" class="form-control" id="dateInput" value="{{ log.log_date.strftime('%Y-%m-%d') }}">
                        </div>
                        <button type="submit" class="btn btn-primary btn-sm btn-block">
                            <i class="fas fa-search mr-1"></i> 查看
                        </button>
                    </form>
                </div>
            </div>

            {% if next_log %}
            <a href="{{ url_for('daily_management.inspections_category_cards', log_id=next_log.id) }}" class="btn btn-primary btn-sm">
                后一天 <i class="fas fa-chevron-right ml-1"></i>
            </a>
            {% else %}
            <button class="btn btn-primary btn-sm disabled" disabled>
                后一天 <i class="fas fa-chevron-right ml-1"></i>
            </button>
            {% endif %}
        </div>
    </div>

    <!-- 检查类别卡片 -->
    <div class="category-cards">
        {% for category in categories %}
        <div class="category-card">
            <div class="category-card-header">
                {{ category }}
                <a href="#" class="text-white" data-toggle="tooltip" title="查看详情">
                    <i class="fas fa-info-circle"></i>
                </a>
            </div>
            <div class="category-card-body">
                <!-- 主要照片 -->
                <div class="category-card-photo">
                    {% set main_photo = category_photos.get(category, {}).get('main') %}
                    {% if main_photo %}
                        <img src="{{ main_photo.file_path }}"
                             alt="{{ category }}"
                             data-onclick="showFullImage(" data-event-id="f879f5d5"{{ main_photo.file_path }}', {{ main_photo.rating or 0 }})">
                    {% else %}
                        <!-- 尝试从三个时段中找一张照片 -->
                        {% set morning = category_inspections.get(category, {}).get('morning') %}
                        {% set noon = category_inspections.get(category, {}).get('noon') %}
                        {% set evening = category_inspections.get(category, {}).get('evening') %}

                        {% if morning and morning.photos and morning.photos|length > 0 %}
                            <img src="{{ morning.photos[0].file_path }}"
                                 alt="{{ category }} - 晨检"
                                 data-onclick="showFullImage(" data-event-id="f879f5d5"{{ morning.photos[0].file_path }}', {{ morning.photos[0].rating or 0 }})">
                        {% elif noon and noon.photos and noon.photos|length > 0 %}
                            <img src="{{ noon.photos[0].file_path }}"
                                 alt="{{ category }} - 午检"
                                 data-onclick="showFullImage(" data-event-id="f879f5d5"{{ noon.photos[0].file_path }}', {{ noon.photos[0].rating or 0 }})">
                        {% elif evening and evening.photos and evening.photos|length > 0 %}
                            <img src="{{ evening.photos[0].file_path }}"
                                 alt="{{ category }} - 晚检"
                                 data-onclick="showFullImage(" data-event-id="f879f5d5"{{ evening.photos[0].file_path }}', {{ evening.photos[0].rating or 0 }})">
                        {% else %}
                            <div class="no-photo">
                                <i class="fas fa-image fa-2x mb-2"></i>
                                <div>暂无照片</div>
                            </div>
                        {% endif %}
                    {% endif %}
                </div>

                <!-- 时段状态 -->
                <div class="inspection-periods">
                    <!-- 晨检 -->
                    {% set morning = category_inspections.get(category, {}).get('morning') %}
                    <div class="period-status"
                         data-action="safe-navigate" data-navigate-code="{% if morning %}location.href=" style="cursor: pointer;"{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='morning', inspection_item=category) }}'{% else %}location.href='{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='morning') }}'{% endif %}">
                        <div class="period-icon text-warning">
                            <i class="fas fa-sun"></i>
                        </div>
                        <div class="period-name">晨检</div>
                        {% if morning %}
                            <div class="period-rating">
                                {% for i in range(5) %}
                                    {% if morning.rating and i < morning.rating %}
                                        <i class="fas fa-star"></i>
                                    {% else %}
                                        <i class="far fa-star"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <div class="period-badge status-normal">正常</div>
                            {% if morning.photos and morning.photos|length > 0 %}
                                <img src="{{ morning.photos[0].file_path }}" class="period-thumbnail" alt="晨检照片">
                            {% endif %}
                        {% else %}
                            <div class="period-badge status-unchecked">未检查</div>
                        {% endif %}
                    </div>

                    <!-- 午检 -->
                    {% set noon = category_inspections.get(category, {}).get('noon') %}
                    <div class="period-status"
                         data-action="safe-navigate" data-navigate-code="{% if noon %}location.href=" style="cursor: pointer;"{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='noon', inspection_item=category) }}'{% else %}location.href='{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='noon') }}'{% endif %}">
                        <div class="period-icon text-primary">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <div class="period-name">午检</div>
                        {% if noon %}
                            <div class="period-rating">
                                {% for i in range(5) %}
                                    {% if noon.rating and i < noon.rating %}
                                        <i class="fas fa-star"></i>
                                    {% else %}
                                        <i class="far fa-star"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <div class="period-badge status-normal">正常</div>
                            {% if noon.photos and noon.photos|length > 0 %}
                                <img src="{{ noon.photos[0].file_path }}" class="period-thumbnail" alt="午检照片">
                            {% endif %}
                        {% else %}
                            <div class="period-badge status-unchecked">未检查</div>
                        {% endif %}
                    </div>

                    <!-- 晚检 -->
                    {% set evening = category_inspections.get(category, {}).get('evening') %}
                    <div class="period-status"
                         data-action="safe-navigate" data-navigate-code="{% if evening %}location.href=" style="cursor: pointer;"{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='evening', inspection_item=category) }}'{% else %}location.href='{{ url_for('daily_management.edit_inspection', log_id=log.id, inspection_type='evening') }}'{% endif %}">
                        <div class="period-icon text-info">
                            <i class="fas fa-moon"></i>
                        </div>
                        <div class="period-name">晚检</div>
                        {% if evening %}
                            <div class="period-rating">
                                {% for i in range(5) %}
                                    {% if evening.rating and i < evening.rating %}
                                        <i class="fas fa-star"></i>
                                    {% else %}
                                        <i class="far fa-star"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <div class="period-badge status-normal">正常</div>
                            {% if evening.photos and evening.photos|length > 0 %}
                                <img src="{{ evening.photos[0].file_path }}" class="period-thumbnail" alt="晚检照片">
                            {% endif %}
                        {% else %}
                            <div class="period-badge status-unchecked">未检查</div>
                        {% endif %}
                    </div>
                </div>

                <!-- 描述 -->
                <div class="category-card-description">
                    {% if category_inspections.get(category, {}).get('morning') and category_inspections.get(category, {}).get('morning').description %}
                        晨检: {{ category_inspections.get(category, {}).get('morning').description|truncate(100) }}
                    {% elif category_inspections.get(category, {}).get('noon') and category_inspections.get(category, {}).get('noon').description %}
                        午检: {{ category_inspections.get(category, {}).get('noon').description|truncate(100) }}
                    {% elif category_inspections.get(category, {}).get('evening') and category_inspections.get(category, {}).get('evening').description %}
                        晚检: {{ category_inspections.get(category, {}).get('evening').description|truncate(100) }}
                    {% else %}
                        <span class="text-muted">暂无描述</span>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- 底部导航 -->
    <div class="d-flex justify-content-between mb-4">
        <div>
            <a href="{{ url_for('daily_management.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-home mr-1"></i> 返回首页
            </a>
            <a href="{{ url_for('daily_management.logs') }}" class="btn btn-info btn-sm ml-2">
                <i class="fas fa-list mr-1"></i> 所有日志
            </a>
        </div>
        <div>
            <a href="{{ url_for('daily_management.inspections', log_id=log.id) }}" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-th-large mr-1"></i> 原卡片视图
            </a>
            <a href="{{ url_for('daily_management.inspections_table', log_id=log.id) }}" class="btn btn-outline-primary btn-sm ml-2">
                <i class="fas fa-table mr-1"></i> 表格视图
            </a>
        </div>
    </div>

    <!-- 调试信息 -->
    {% if current_user.is_authenticated and current_user.is_admin %}
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">调试信息</h6>
        </div>
        <div class="card-body">
            <h6>类别照片数据:</h6>
            <pre>{{ category_photos|tojson(indent=2) }}</pre>

            <h6>检查记录数据:</h6>
            {% for category in categories %}
                <div class="mb-3">
                    <strong>{{ category }}:</strong>
                    <ul>
                        {% for period_type, inspection in category_inspections.get(category, {}).items() %}
                            {% if inspection %}
                                <li>
                                    {{ period_type }}: ID={{ inspection.id }}
                                    {% if inspection.photos %}
                                        ({{ inspection.photos|length }}张照片)
                                        {% for photo in inspection.photos %}
                                            <div>照片{{ loop.index }}: {{ photo.file_path }}</div>
                                        {% endfor %}
                                    {% else %}
                                        (无照片)
                                    {% endif %}
                                </li>
                            {% endif %}
                        {% endfor %}
                    </ul>
                </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    // 初始化工具提示
    $(function () {
        $('[data-toggle="tooltip"]').tooltip();
    });

    // 显示大图
    function showFullImage(imageSrc, rating) {
        // 构建星级评分HTML
        let ratingHtml = '';
        for (let i = 1; i <= 5; i++) {
            if (i <= rating) {
                ratingHtml += '<i class="fas fa-star text-warning"></i>';
            } else {
                ratingHtml += '<i class="far fa-star text-warning"></i>';
            }
        }

        // 使用SweetAlert2显示大图
        Swal.fire({
            imageUrl: imageSrc,
            imageAlt: '检查照片',
            html: `
                <div class="mt-3">
                    <div class="rating mb-2">${ratingHtml}</div>
                </div>
            `,
            showCloseButton: true,
            showConfirmButton: false,
            width: 'auto',
            padding: '1rem',
            background: '#fff',
            backdrop: 'rgba(0,0,0,0.8)'
        });
    }

    // 跳转到指定日期
    function goToDate(event) {
        event.preventDefault();
        const dateInput = document.getElementById('dateInput');
        const selectedDate = dateInput.value;

        if (selectedDate) {
            window.location.href = "{{ url_for('daily_management.inspections_by_date', date_str='') }}" + selectedDate + "?view=category_cards";
        }
    }

    // 键盘导航
    document.addEventListener('keydown', function(e) {
        // 左箭头键 - 前一天
        if (e.keyCode === 37) {
            const prevBtn = document.querySelector('.nav-buttons a:first-child');
            if (prevBtn && !prevBtn.classList.contains('disabled')) {
                prevBtn.click();
            }
        }
        // 右箭头键 - 后一天
        else if (e.keyCode === 39) {
            const nextBtn = document.querySelector('.nav-buttons a:last-child');
            if (nextBtn && !nextBtn.classList.contains('disabled')) {
                nextBtn.click();
            }
        }
    });
</script>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}
