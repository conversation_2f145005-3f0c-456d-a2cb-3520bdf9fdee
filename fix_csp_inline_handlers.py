#!/usr/bin/env python3
"""
CSP内联事件处理器修复工具
自动扫描并修复模板文件中的内联事件处理器，使其符合CSP安全策略
"""

import os
import re
import shutil
from pathlib import Path
from datetime import datetime

class CSPInlineHandlerFixer:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.template_dirs = [
            self.project_root / "app" / "templates",
        ]
        self.backup_dir = self.project_root / "csp_fix_backup"
        self.fixed_files = []
        self.errors = []
        
        # 内联事件处理器模式
        self.inline_patterns = [
            # onclick="function()"
            (r'onclick\s*=\s*["\']([^"\']+)["\']', 'click'),
            # onchange="function()"
            (r'onchange\s*=\s*["\']([^"\']+)["\']', 'change'),
            # onsubmit="function()"
            (r'onsubmit\s*=\s*["\']([^"\']+)["\']', 'submit'),
            # onload="function()"
            (r'onload\s*=\s*["\']([^"\']+)["\']', 'load'),
            # onfocus="function()"
            (r'onfocus\s*=\s*["\']([^"\']+)["\']', 'focus'),
            # onblur="function()"
            (r'onblur\s*=\s*["\']([^"\']+)["\']', 'blur'),
            # onmouseover="function()"
            (r'onmouseover\s*=\s*["\']([^"\']+)["\']', 'mouseover'),
            # onmouseout="function()"
            (r'onmouseout\s*=\s*["\']([^"\']+)["\']', 'mouseout'),
        ]
    
    def create_backup(self):
        """创建备份目录"""
        if self.backup_dir.exists():
            shutil.rmtree(self.backup_dir)
        self.backup_dir.mkdir(parents=True)
        print(f"✅ 创建备份目录: {self.backup_dir}")
    
    def backup_file(self, file_path):
        """备份单个文件"""
        relative_path = file_path.relative_to(self.project_root)
        backup_path = self.backup_dir / relative_path
        backup_path.parent.mkdir(parents=True, exist_ok=True)
        shutil.copy2(file_path, backup_path)
        return backup_path
    
    def find_template_files(self):
        """查找所有模板文件"""
        template_files = []
        for template_dir in self.template_dirs:
            if template_dir.exists():
                for file_path in template_dir.rglob("*.html"):
                    template_files.append(file_path)
        return template_files
    
    def generate_event_id(self, handler_code):
        """为事件处理器生成唯一ID"""
        import hashlib
        return hashlib.md5(handler_code.encode()).hexdigest()[:8]
    
    def fix_inline_handlers(self, content, file_path):
        """修复文件中的内联事件处理器"""
        fixed_content = content
        changes_made = []

        # 先检查是否已经有data-onclick等属性，如果有则跳过
        if ('data-onclick=' in fixed_content or 'data-onchange=' in fixed_content or
            'data-onsubmit=' in fixed_content or 'data-onload=' in fixed_content):
            return fixed_content, changes_made

        for pattern, event_type in self.inline_patterns:
            matches = re.finditer(pattern, fixed_content, re.IGNORECASE)
            for match in reversed(list(matches)):  # 从后往前处理，避免位置偏移
                full_match = match.group(0)
                handler_code = match.group(1)
                event_id = self.generate_event_id(handler_code)

                # 替换为data-onclick属性（不需要data-event-id）
                if event_type == 'click':
                    replacement = f'data-onclick="{handler_code}"'
                else:
                    replacement = f'data-on{event_type}="{handler_code}"'

                # 执行替换
                start, end = match.span()
                fixed_content = fixed_content[:start] + replacement + fixed_content[end:]

                changes_made.append({
                    'original': full_match,
                    'replacement': replacement,
                    'event_type': event_type,
                    'handler_code': handler_code,
                    'event_id': event_id
                })

        return fixed_content, changes_made
    
    def ensure_universal_handler_script(self, content):
        """确保页面包含通用事件处理器脚本"""
        # 检查是否已经包含了通用事件处理器
        if 'universal-event-handler.js' in content:
            return content
        
        # 在</body>标签前添加脚本
        script_tag = '''
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>'''
        
        if '</body>' in content:
            content = content.replace('</body>', script_tag + '\n</body>')
        elif '{% endblock %}' in content:
            # 如果是模板继承，在endblock前添加
            content = content.replace('{% endblock %}', script_tag + '\n{% endblock %}')
        
        return content
    
    def fix_file(self, file_path):
        """修复单个文件"""
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # 修复内联事件处理器
            fixed_content, changes = self.fix_inline_handlers(original_content, file_path)
            
            if changes:
                # 备份原文件
                backup_path = self.backup_file(file_path)
                
                # 确保包含通用事件处理器脚本
                fixed_content = self.ensure_universal_handler_script(fixed_content)
                
                # 写入修复后的内容
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(fixed_content)
                
                self.fixed_files.append({
                    'file': file_path,
                    'backup': backup_path,
                    'changes': changes
                })
                
                print(f"✅ 修复文件: {file_path.relative_to(self.project_root)} ({len(changes)} 个内联处理器)")
                for change in changes:
                    print(f"   - {change['event_type']}: {change['original'][:50]}...")
            
        except Exception as e:
            error_msg = f"修复文件 {file_path} 时出错: {str(e)}"
            self.errors.append(error_msg)
            print(f"❌ {error_msg}")
    
    def run(self, dry_run=False):
        """运行修复工具"""
        print("🔧 CSP内联事件处理器修复工具")
        print("=" * 50)

        if dry_run:
            print("🔍 干运行模式 - 只检查不修复")

        # 查找模板文件
        template_files = self.find_template_files()
        print(f"📁 找到 {len(template_files)} 个模板文件")

        if not dry_run:
            # 创建备份
            self.create_backup()

        # 检查/修复每个文件
        for file_path in template_files:
            if dry_run:
                self.check_file(file_path)
            else:
                self.fix_file(file_path)

        # 输出结果
        print("\n" + "=" * 50)
        if dry_run:
            print("🔍 检查完成!")
            total_handlers = sum(len(item['changes']) for item in self.fixed_files)
            print(f"📊 找到 {total_handlers} 个内联事件处理器")
            print(f"📁 涉及 {len(self.fixed_files)} 个文件")
        else:
            print("🎉 修复完成!")
            print(f"✅ 成功修复: {len(self.fixed_files)} 个文件")
            print(f"❌ 修复失败: {len(self.errors)} 个文件")

        if self.fixed_files:
            if not dry_run:
                print(f"\n📦 备份位置: {self.backup_dir}")
            print(f"\n{'检查到的' if dry_run else '修复的'}文件:")
            for item in self.fixed_files:
                rel_path = item['file'].relative_to(self.project_root)
                print(f"  - {rel_path} ({len(item['changes'])} 个{'内联处理器' if dry_run else '修复'})")

        if self.errors:
            print("\n❌ 错误:")
            for error in self.errors:
                print(f"  - {error}")

        if not dry_run:
            print(f"\n💡 提示:")
            print(f"  1. 请确保 app/static/js/universal-event-handler.js 文件存在")
            print(f"  2. 如果修复有问题，可以从 {self.backup_dir} 恢复文件")
            print(f"  3. 重启应用程序以应用更改")

    def check_file(self, file_path):
        """检查单个文件（干运行模式）"""
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 检查内联事件处理器
            _, changes = self.fix_inline_handlers(content, file_path)

            if changes:
                self.fixed_files.append({
                    'file': file_path,
                    'backup': None,
                    'changes': changes
                })

                print(f"🔍 检查文件: {file_path.relative_to(self.project_root)} ({len(changes)} 个内联处理器)")
                for change in changes[:3]:  # 只显示前3个
                    print(f"   - {change['event_type']}: {change['original'][:50]}...")
                if len(changes) > 3:
                    print(f"   ... 还有 {len(changes) - 3} 个")

        except Exception as e:
            error_msg = f"检查文件 {file_path} 时出错: {str(e)}"
            self.errors.append(error_msg)
            print(f"❌ {error_msg}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='CSP内联事件处理器修复工具')
    parser.add_argument('--project-root', default='.', help='项目根目录路径')
    parser.add_argument('--dry-run', action='store_true', help='只检查不修复')
    
    args = parser.parse_args()
    
    fixer = CSPInlineHandlerFixer(args.project_root)
    fixer.run(dry_run=args.dry_run)

if __name__ == "__main__":
    main()
